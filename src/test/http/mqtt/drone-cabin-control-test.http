### 无人机货仓控制接口完整测试
### 基础配置
@baseUrl = http://localhost:8080
@deviceId = 49004B001151323532363931

### ========== 通用控制接口（严格按照MQTT协议） ==========

### 1. 舱盖控制 - 打开 (action=101, action_code=1)
POST {{baseUrl}}/api/drone/{{deviceId}}/command
Content-Type: application/json

{
  "action": 101,
  "action_code": 1
}

### 2. 舱盖控制 - 关闭 (action=101, action_code=2)
POST {{baseUrl}}/api/drone/{{deviceId}}/command
Content-Type: application/json

{
  "action": 101,
  "action_code": 2
}

### 3. 四方推杆控制 - 推中 (action=102, action_code=1)
POST {{baseUrl}}/api/drone/{{deviceId}}/command
Content-Type: application/json

{
  "action": 102,
  "action_code": 1
}

### 4. 四方推杆控制 - 归位 (action=102, action_code=2)
POST {{baseUrl}}/api/drone/{{deviceId}}/command
Content-Type: application/json

{
  "action": 102,
  "action_code": 2
}

### 5. 取货口控制 - 开门 (action=103, action_code=1)
POST {{baseUrl}}/api/drone/{{deviceId}}/command
Content-Type: application/json

{
  "action": 103,
  "action_code": 1
}

### 6. 取货口控制 - 关门 (action=103, action_code=2)
POST {{baseUrl}}/api/drone/{{deviceId}}/command
Content-Type: application/json

{
  "action": 103,
  "action_code": 2
}

### 7. 送货口控制 - 开门 (action=104, action_code=1)
POST {{baseUrl}}/api/drone/{{deviceId}}/command
Content-Type: application/json

{
  "action": 104,
  "action_code": 1
}

### 8. 送货口控制 - 关门 (action=104, action_code=2)
POST {{baseUrl}}/api/drone/{{deviceId}}/command
Content-Type: application/json

{
  "action": 104,
  "action_code": 2
}

### 9. 电子秤 - 读取重量 (action=105, action_code=1)
POST {{baseUrl}}/api/drone/{{deviceId}}/command
Content-Type: application/json

{
  "action": 105,
  "action_code": 1
}

### 10. 视频监控 - 启动 (action=106, action_code=1)
POST {{baseUrl}}/api/drone/{{deviceId}}/command
Content-Type: application/json

{
  "action": 106,
  "action_code": 1
}

### 11. 视频监控 - 停止 (action=106, action_code=2)
POST {{baseUrl}}/api/drone/{{deviceId}}/command
Content-Type: application/json

{
  "action": 106,
  "action_code": 2
}

### 12. 升降台控制 - 归位 (action=107, action_code=1)
POST {{baseUrl}}/api/drone/{{deviceId}}/command
Content-Type: application/json

{
  "action": 107,
  "action_code": 1
}

### 13. 升降台控制 - 取货口位 (action=107, action_code=2)
POST {{baseUrl}}/api/drone/{{deviceId}}/command
Content-Type: application/json

{
  "action": 107,
  "action_code": 2
}

### 14. 升降台控制 - 送货口位 (action=107, action_code=3)
POST {{baseUrl}}/api/drone/{{deviceId}}/command
Content-Type: application/json

{
  "action": 107,
  "action_code": 3
}

### 15. 升降台控制 - 取货口位(下降) (action=107, action_code=4)
POST {{baseUrl}}/api/drone/{{deviceId}}/command
Content-Type: application/json

{
  "action": 107,
  "action_code": 4
}

### 16. 升降台控制 - 送货口位(下降) (action=107, action_code=5)
POST {{baseUrl}}/api/drone/{{deviceId}}/command
Content-Type: application/json

{
  "action": 107,
  "action_code": 5
}

### 17. 升降台控制 - 停机坪位 (action=107, action_code=6)
POST {{baseUrl}}/api/drone/{{deviceId}}/command
Content-Type: application/json

{
  "action": 107,
  "action_code": 6
}

### 18. 货仓推杆控制 - 推 (action=108, action_code=1)
POST {{baseUrl}}/api/drone/{{deviceId}}/command
Content-Type: application/json

{
  "action": 108,
  "action_code": 1
}

### 19. 货仓推杆控制 - 收 (action=108, action_code=2)
POST {{baseUrl}}/api/drone/{{deviceId}}/command
Content-Type: application/json

{
  "action": 108,
  "action_code": 2
}

### 20. 设置上报周期 - 30秒 (action=109, action_code=30)
POST {{baseUrl}}/api/drone/{{deviceId}}/command
Content-Type: application/json

{
  "action": 109,
  "action_code": 30
}

### 21. 固件更新 (action=110, action_code=1)
POST {{baseUrl}}/api/drone/{{deviceId}}/command
Content-Type: application/json

{
  "action": 110,
  "action_code": 1
}

### ========== 批量控制测试 ==========

### 22. 批量控制 - 简单舱盖操作 (action=111)
POST {{baseUrl}}/api/drone/{{deviceId}}/command
Content-Type: application/json

{
  "action": 111,
  "data": {
    "commands": [
      {
        "action": 101,
        "action_code": 1,
        "delay": 0,
        "params": null
      },
      {
        "action": 101,
        "action_code": 2,
        "delay": 2000,
        "params": null
      }
    ]
  }
}

### 23. 批量控制 - 完整取货流程
POST {{baseUrl}}/api/drone/{{deviceId}}/command
Content-Type: application/json

{
  "action": 111,
  "data": {
    "commands": [
      {
        "action": 101,
        "action_code": 1,
        "delay": 0,
        "params": null
      },
      {
        "action": 102,
        "action_code": 1,
        "delay": 1000,
        "params": null
      },
      {
        "action": 107,
        "action_code": 2,
        "delay": 2000,
        "params": null
      },
      {
        "action": 103,
        "action_code": 1,
        "delay": 1500,
        "params": null
      },
      {
        "action": 103,
        "action_code": 2,
        "delay": 3000,
        "params": null
      },
      {
        "action": 107,
        "action_code": 1,
        "delay": 1000,
        "params": null
      },
      {
        "action": 101,
        "action_code": 2,
        "delay": 1500,
        "params": null
      }
    ]
  }
}

### ========== 便捷接口测试 ==========

### 24. 便捷接口 - 打开舱盖
POST {{baseUrl}}/api/drone/{{deviceId}}/cover/open

### 25. 便捷接口 - 关闭舱盖
POST {{baseUrl}}/api/drone/{{deviceId}}/cover/close

### 26. 便捷接口 - 推动推杆
POST {{baseUrl}}/api/drone/{{deviceId}}/lever/push

### 27. 便捷接口 - 重置推杆
POST {{baseUrl}}/api/drone/{{deviceId}}/lever/reset

### 28. 便捷接口 - 打开取货口
POST {{baseUrl}}/api/drone/{{deviceId}}/pickup/open

### 29. 便捷接口 - 关闭取货口
POST {{baseUrl}}/api/drone/{{deviceId}}/pickup/close

### 30. 便捷接口 - 打开送货口
POST {{baseUrl}}/api/drone/{{deviceId}}/delivery/open

### 31. 便捷接口 - 关闭送货口
POST {{baseUrl}}/api/drone/{{deviceId}}/delivery/close

### 32. 便捷接口 - 读取重量
POST {{baseUrl}}/api/drone/{{deviceId}}/scale/read

### ========== 特殊控制接口测试 ==========

### 33. 启动视频流
POST {{baseUrl}}/api/drone/{{deviceId}}/video/start?streamId=1&rtspUrl=rtsp://example.com/stream1

### 34. 停止视频流
POST {{baseUrl}}/api/drone/{{deviceId}}/video/stop?streamId=1

### 35. 升降台下降到取货口位
POST {{baseUrl}}/api/drone/{{deviceId}}/platform/lower-to-pickup

### 36. 升降台下降到送货口位
POST {{baseUrl}}/api/drone/{{deviceId}}/platform/lower-to-delivery

### 37. 升降台升到停机坪位
POST {{baseUrl}}/api/drone/{{deviceId}}/platform/rise-to-landing

### 38. 推动货仓推杆
POST {{baseUrl}}/api/drone/{{deviceId}}/cargo-pusher/push

### 39. 收回货仓推杆
POST {{baseUrl}}/api/drone/{{deviceId}}/cargo-pusher/retract

### 40. 设置状态上报周期
POST {{baseUrl}}/api/drone/{{deviceId}}/report-interval?duration=60

### 41. 固件更新
POST {{baseUrl}}/api/drone/{{deviceId}}/firmware/update?url=http://example.com/firmware.bin&version=1.2.3&checksum=abc123

### ========== 批量控制接口测试 ==========

### 42. 批量控制（协议标准格式）
POST {{baseUrl}}/api/drone/{{deviceId}}/batch/protocol
Content-Type: application/json

{
  "action": 111,
  "data": {
    "commands": [
      {
        "action": 101,
        "action_code": 1,
        "delay": 0,
        "params": null
      }
    ]
  }
}

### 43. 批量控制（扩展格式）
POST {{baseUrl}}/api/drone/{{deviceId}}/batch
Content-Type: application/json

{
  "action": 111,
  "data": {
    "commands": [
      {
        "action": 101,
        "action_code": 1,
        "delay": 0,
        "params": null
      }
    ]
  }
}

### ========== 状态查询测试 ==========

### 44. 获取设备状态
GET {{baseUrl}}/api/drone/{{deviceId}}/status

### 45. 获取告警信息
GET {{baseUrl}}/api/drone/{{deviceId}}/warnings

### 46. 获取在线设备列表
GET {{baseUrl}}/api/drone/online

### 47. 清除设备缓存
DELETE {{baseUrl}}/api/drone/{{deviceId}}/cache

### ========== 错误测试 ==========

### 48. 测试无效action编号
POST {{baseUrl}}/api/drone/{{deviceId}}/command
Content-Type: application/json

{
  "action": 999,
  "action_code": 1
}

### 49. 测试缺少action参数
POST {{baseUrl}}/api/drone/{{deviceId}}/command
Content-Type: application/json

{
  "action_code": 1
}

### 50. 测试缺少action_code参数
POST {{baseUrl}}/api/drone/{{deviceId}}/command
Content-Type: application/json

{
  "action": 101
}

### ========== 无人机即将到达测试 ==========

### 51. 无人机即将到达通知
POST {{baseUrl}}/api/drone/SN001/approaching?taskId=123
