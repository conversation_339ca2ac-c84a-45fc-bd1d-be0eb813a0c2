### 获取无人机列表
GET http://localhost:8080/api/backend/drone-devices?page=1&size=10&status=1
Content-Type: application/json
Delivery-Auth: {{authToken}}

### 注册新无人机
POST http://localhost:8080/api/backend/drones
Content-Type: application/json
Delivery-Auth: {{authToken}}

{
  "droneId": "DRONE-002",
  "model": "DJI M300",
  "maxWeight": 10.5,
  "status": "ONLINE"
}

### 更新无人机状态
PUT http://localhost:8080/api/backend/drones/DRONE-001/status
Content-Type: application/json
Delivery-Auth: {{authToken}}

{
  "status": "MAINTENANCE",
  "remark": "定期维护"
}

### 获取无人机详情
GET http://localhost:8080/api/backend/drone-devices/1
Content-Type: application/json
Delivery-Auth: {{authToken}}

### 创建飞行任务
POST https://unit.xiaoliangma.com/api/lup-cds-djicloud/v1/wayline-job/by/create-flight-task
Content-Type: application/json
<PERSON>-Auth: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhY2NvdW50SWQiOjE5NDMyMjg5NTU2MDcyMTYxMzAsInVzZXJfbmFtZSI6IjE3NjgxMDEyNDgxIiwic2NvcGUiOlsiYWxsIl0sImlwIjoiNjAuMTc0LjE2OC4xMTEiLCJuYW1lIjoi6ams6LaFIiwiYXZhdGFyIjpudWxsLCJleHAiOjE3NTQ3OTY3MzUsInVzZXJOYW1lIjoiMTc2ODEwMTI0ODEiLCJ0eXBlIjoxLCJhdXRob3JpdGllcyI6WyIxODM0NDE4MzIxMzI4NDQzMzkzIiwiMTcxNTI1MTE4MjA3MzExODcyMSIsIjE4ODk1NDM2ODQzMzIxMTgwMTciLCIxNzE1MjUxMDg4NjQwODAyODE4IiwiMTgwMDc3MTY0MjIwNTIyNDk2MSIsIjE4MzQ0MTUyMzYzODAxMDI2NTciLCIxODM0NDE1NzI3NzAwODczMjE3IiwiMTgzNDQxNDA0MTQ3NzQxOTAwOSIsIjE3MTUyNTAyODQ2MzA4OTY2NDEiLCIxOTMwNTI0MjI1MDA2NzA2NjkwIiwiMTY1OTM2MjUxODU1MjM3OTM5NCIsIjE3MTUyNTEyOTMxNTA4NzE1NTMiLCIxNzE1MjUxMzM2NjI5MDI2ODE4IiwiMTkzMDE2MTY1MTg3MDMxMDQwMiIsIjE4NTA3MTMyMDg0MTY2MTY0NDkiLCIxNzE1MjUxMjUzMjg4MjA2MzM3IiwiMTgzNDQxNDg3OTkxMDQwMDAwMSIsIjE3MTUyNTA1NjQxOTM4NDExNTMiLCIxNzE1MjUwNzg0NTUzNzYyODE3IiwiMTgzNDQxNjAxODc4OTc2NTEyMiJdLCJqdGkiOiJiOTlkZGIzZC03MTI5LTQ5YTYtODE0OC1mZDQ2MzNjMTY1NjIiLCJjbGllbnRfaWQiOiJ1YXZmY19hZG1pbiJ9.zq4TsWo3pygInrw76u3lkLS1BgLLU7ZtG9osNJdzWH0

{
  "deviceSn": "BY715225020031",
  "name": "测试飞行任务",
  "alt": 50.0,
  "targetStoreSn": "1234",
  "startStoreSn": "234567",
  "speed": "12",
  "finish": "2"
}
