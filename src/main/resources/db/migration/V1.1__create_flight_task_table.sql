-- 飞行任务表
DROP TABLE IF EXISTS `lalp_flight_task`;
CREATE TABLE `lalp_flight_task` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `flight_task_id` varchar(100) NOT NULL COMMENT '飞控系统返回的飞行任务ID',
    `delivery_task_id` bigint NULL DEFAULT NULL COMMENT '关联的配送任务ID',
    `device_sn` varchar(50) NOT NULL COMMENT '设备SN（无人机编号）',
    `task_name` varchar(100) NOT NULL COMMENT '任务名称',
    `altitude` double NOT NULL COMMENT '飞行高度（米）',
    `start_store_sn` varchar(50) NOT NULL COMMENT '起飞点物流仓SN',
    `target_store_sn` varchar(50) NOT NULL COMMENT '目标点物流仓SN',
    `speed` varchar(10) DEFAULT '12' COMMENT '全局飞行速度（m/s）',
    `finish_action` varchar(10) DEFAULT '2' COMMENT '航线完成时动作（0=返航；1=悬停；2=降落）',
    `status` varchar(20) NOT NULL DEFAULT 'CREATED' COMMENT '飞行任务状态',
    `remote_response` text COMMENT '远程飞控系统的完整响应',
    `error_message` text COMMENT '错误信息（失败时）',
    `error_code` varchar(50) COMMENT '错误代码（失败时）',
    `created_by` varchar(50) COMMENT '创建人',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `start_time` datetime COMMENT '任务开始时间',
    `end_time` datetime COMMENT '任务结束时间',
    `tenant_id` bigint NOT NULL COMMENT '租户ID',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `uk_flight_task_id` (`flight_task_id`) USING BTREE,
    KEY `idx_delivery_task_id` (`delivery_task_id`) USING BTREE,
    KEY `idx_device_sn` (`device_sn`) USING BTREE,
    KEY `idx_status` (`status`) USING BTREE,
    KEY `idx_tenant_id` (`tenant_id`) USING BTREE,
    KEY `idx_create_time` (`create_time`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='飞行任务表';
