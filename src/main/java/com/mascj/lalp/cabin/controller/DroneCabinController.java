package com.mascj.lalp.cabin.controller;

import com.mascj.lalp.cabin.api.*;
import com.mascj.lalp.cabin.dto.DeviceStatusResponse;
import com.mascj.lalp.cabin.dto.DeviceWarningsResponse;
import com.mascj.lalp.cabin.dto.DroneApproachingResponse;

import com.mascj.lalp.cabin.service.DroneApproachingService;
import com.mascj.lalp.cabin.service.DroneCabinService;
import com.mascj.lalp.infrastructure.common.api.ApiResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 无人机货仓控制接口
 */
@Slf4j
@Tag(name = "无人机货仓控制", description = "控制无人机货仓开关和状态查询")
@RestController
@RequestMapping("/api/drone")
@RequiredArgsConstructor
public class DroneCabinController {

    private final DroneCabinService droneCabinService;
    private final DroneApproachingService droneApproachingService;

    @Operation(summary = "无人机即将到达通知",
               description = "无人机即将到达时调用此接口，根据无人机SN号查找配送任务，然后打开对应物流货仓的货舱盖")
    @PostMapping("/{droneSn}/approaching")
    public ApiResult<DroneApproachingResponse> droneApproaching(
            @Parameter(description = "无人机SN号", required = true, example = "SN001")
            @PathVariable String droneSn,
            @Parameter(description = "任务ID（可选）", example = "123")
            @RequestParam(required = false) String taskId) {

        // 调用服务处理无人机即将到达逻辑
        DroneApproachingService.DroneApproachingResult result =
                droneApproachingService.handleDroneApproaching(droneSn, taskId);

        // 使用实体类的静态方法创建响应
        return DroneApproachingResponse.fromResult(result, droneSn, taskId);
    }

    // ========== 通用控制接口 ==========

    /**
     * 通用设备控制接口 - 支持单个命令和批量命令
     * 严格按照MQTT协议文档实现，支持101-111所有控制指令
     *
     * 支持两种格式：
     * 1. 单个命令: {"action": 101, "action_code": 1}
     * 2. 批量命令: {"action": 111, "data": {"commands": [...]}}
     *
     * @param deviceId 设备ID
     * @param request 控制请求体，支持单个和批量命令
     * @return 控制结果
     */
    @Operation(summary = "通用设备控制接口",
               description = "支持单个命令和批量命令，严格按照MQTT协议格式。单个命令格式：{\"action\":101,\"action_code\":1}，批量命令格式：{\"action\":111,\"data\":{\"commands\":[...]}}")
    @PostMapping("/{deviceId}/command")
    public ResponseEntity<Map<String, Object>> universalControl(
            @Parameter(description = "设备ID", required = true, example = "49004A001151323532363931")
            @PathVariable String deviceId,
            @RequestBody Map<String, Object> request) {

        log.info("通用设备控制: deviceId={}, request={}", deviceId, request);

        try {
            // 调用Service执行业务逻辑
            Map<String, Object> result = droneCabinService.executeUniversalControl(deviceId, request);
            return ResponseEntity.ok(result);

        } catch (IllegalArgumentException e) {
            // 参数验证异常
            log.warn("参数验证失败: deviceId={}, request={}, error={}", deviceId, request, e.getMessage());
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", e.getMessage(),
                "deviceId", deviceId,
                "timestamp", System.currentTimeMillis()
            ));

        } catch (Exception e) {
            // 其他业务异常
            log.error("通用设备控制失败: deviceId={}, request={}", deviceId, request, e);
            return ResponseEntity.status(500).body(Map.of(
                "success", false,
                "message", "控制失败: " + e.getMessage(),
                "deviceId", deviceId,
                "timestamp", System.currentTimeMillis()
            ));
        }
    }

    /**
     * 内部方法：便捷接口的统一入口
     * 只负责HTTP层面的处理，业务逻辑委托给Service
     */
    private ResponseEntity<Map<String, Object>> controlDevice(String deviceId, String action, String actionCode, boolean withStatus) {
        try {
            // 调用Service执行业务逻辑
            Map<String, Object> result = droneCabinService.executeDeviceControl(deviceId, action, actionCode, withStatus);
            return ResponseEntity.ok(result);

        } catch (IllegalArgumentException e) {
            // 参数验证异常
            log.warn("参数验证失败: deviceId={}, action={}, actionCode={}, error={}",
                    deviceId, action, actionCode, e.getMessage());
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", e.getMessage(),
                "deviceId", deviceId,
                "action", action + "_" + actionCode,
                "timestamp", System.currentTimeMillis()
            ));

        } catch (Exception e) {
            // 其他业务异常
            log.error("设备控制失败: deviceId={}, action={}, actionCode={}",
                    deviceId, action, actionCode, e);
            return ResponseEntity.status(500).body(Map.of(
                "success", false,
                "message", "控制失败: " + e.getMessage(),
                "deviceId", deviceId,
                "action", action + "_" + actionCode,
                "timestamp", System.currentTimeMillis()
            ));
        }
    }

    // ========== 便捷接口（基于统一接口） ==========

    @Operation(summary = "打开货仓盖", description = "打开指定设备的货仓盖")
    @PostMapping("/{deviceId}/cover/open")
    public ResponseEntity<Map<String, Object>> openCover(
            @Parameter(description = "设备ID", required = true)
            @PathVariable String deviceId,
            @Parameter(description = "是否返回状态", example = "true")
            @RequestParam(defaultValue = "true") boolean withStatus) {
        return controlDevice(deviceId, "cover", "open", withStatus);
    }

    @Operation(summary = "关闭货仓盖", description = "关闭指定设备的货仓盖")
    @PostMapping("/{deviceId}/cover/close")
    public ResponseEntity<Map<String, Object>> closeCover(
            @Parameter(description = "设备ID", required = true)
            @PathVariable String deviceId,
            @Parameter(description = "是否返回状态", example = "true")
            @RequestParam(defaultValue = "true") boolean withStatus) {
        return controlDevice(deviceId, "cover", "close", withStatus);
    }

    @Operation(summary = "推动推杆", description = "推动无人机的推杆")
    @PostMapping("/{deviceId}/lever/push")
    public ResponseEntity<Map<String, Object>> pushLever(
            @Parameter(description = "设备ID", required = true)
            @PathVariable String deviceId,
            @Parameter(description = "是否返回状态", example = "true")
            @RequestParam(defaultValue = "true") boolean withStatus) {
        return controlDevice(deviceId, "lever", "push", withStatus);
    }

    @Operation(summary = "重置推杆", description = "重置无人机的推杆位置")
    @PostMapping("/{deviceId}/lever/reset")
    public ResponseEntity<Map<String, Object>> resetLever(
            @Parameter(description = "设备ID", required = true)
            @PathVariable String deviceId,
            @Parameter(description = "是否返回状态", example = "true")
            @RequestParam(defaultValue = "true") boolean withStatus) {
        return controlDevice(deviceId, "lever", "reset", withStatus);
    }

    @Operation(summary = "打开取货口", description = "打开指定设备的取货口")
    @PostMapping("/{deviceId}/pickup/open")
    public ResponseEntity<Map<String, Object>> openPickup(
            @Parameter(description = "设备ID", required = true)
            @PathVariable String deviceId,
            @Parameter(description = "是否返回状态", example = "true")
            @RequestParam(defaultValue = "true") boolean withStatus) {
        return controlDevice(deviceId, "pickup", "open", withStatus);
    }

    @Operation(summary = "关闭取货口", description = "关闭指定设备的取货口")
    @PostMapping("/{deviceId}/pickup/close")
    public ResponseEntity<Map<String, Object>> closePickup(
            @Parameter(description = "设备ID", required = true)
            @PathVariable String deviceId,
            @Parameter(description = "是否返回状态", example = "true")
            @RequestParam(defaultValue = "true") boolean withStatus) {
        return controlDevice(deviceId, "pickup", "close", withStatus);
    }

    @Operation(summary = "打开送货口", description = "打开指定设备的送货口")
    @PostMapping("/{deviceId}/delivery/open")
    public ResponseEntity<Map<String, Object>> openDelivery(
            @Parameter(description = "设备ID", required = true)
            @PathVariable String deviceId,
            @Parameter(description = "是否返回状态", example = "true")
            @RequestParam(defaultValue = "true") boolean withStatus) {
        return controlDevice(deviceId, "delivery", "open", withStatus);
    }

    @Operation(summary = "关闭送货口", description = "关闭指定设备的送货口")
    @PostMapping("/{deviceId}/delivery/close")
    public ResponseEntity<Map<String, Object>> closeDelivery(
            @Parameter(description = "设备ID", required = true)
            @PathVariable String deviceId,
            @Parameter(description = "是否返回状态", example = "true")
            @RequestParam(defaultValue = "true") boolean withStatus) {
        return controlDevice(deviceId, "delivery", "close", withStatus);
    }

    @Operation(summary = "读取电子秤重量", description = "读取指定设备的电子秤重量")
    @PostMapping("/{deviceId}/scale/read")
    public ResponseEntity<Map<String, Object>> readScale(
            @Parameter(description = "设备ID", required = true)
            @PathVariable String deviceId,
            @Parameter(description = "是否返回状态", example = "true")
            @RequestParam(defaultValue = "true") boolean withStatus) {
        return controlDevice(deviceId, "scale", "read", withStatus);
    }

    // ========== 特殊控制接口（有额外参数的保留原样） ==========

    @Operation(summary = "启动视频流", description = "启动指定设备的视频流")
    @PostMapping("/{deviceId}/video/start")
    public ResponseEntity<Map<String, Object>> startVideoStream(
            @Parameter(description = "设备ID", required = true)
            @PathVariable String deviceId,
            @Parameter(description = "视频流编号", required = true, example = "1")
            @RequestParam int streamId,
            @Parameter(description = "RTSP流地址", required = true, example = "rtsp://example.com/stream1")
            @RequestParam String rtspUrl) {

        log.info("启动视频流: deviceId={}, streamId={}, rtspUrl={}", deviceId, streamId, rtspUrl);

        try {
            droneCabinService.sendControlCommand(deviceId, VideoStreamCommand.startStream(streamId, rtspUrl));
            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "视频流启动指令发送成功",
                "deviceId", deviceId,
                "streamId", streamId,
                "rtspUrl", rtspUrl,
                "timestamp", System.currentTimeMillis()
            ));
        } catch (Exception e) {
            log.error("启动视频流失败: deviceId={}, streamId={}", deviceId, streamId, e);
            return ResponseEntity.ok(Map.of(
                "success", false,
                "message", "启动视频流失败: " + e.getMessage(),
                "deviceId", deviceId,
                "streamId", streamId,
                "timestamp", System.currentTimeMillis()
            ));
        }
    }

    @Operation(summary = "停止视频流", description = "停止指定设备的视频流")
    @PostMapping("/{deviceId}/video/stop")
    public ResponseEntity<Map<String, Object>> stopVideoStream(
            @Parameter(description = "设备ID", required = true)
            @PathVariable String deviceId,
            @Parameter(description = "视频流编号", required = true, example = "1")
            @RequestParam int streamId) {

        log.info("停止视频流: deviceId={}, streamId={}", deviceId, streamId);

        try {
            droneCabinService.sendControlCommand(deviceId, VideoStreamCommand.stopStream(streamId));
            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "视频流停止指令发送成功",
                "deviceId", deviceId,
                "streamId", streamId,
                "timestamp", System.currentTimeMillis()
            ));
        } catch (Exception e) {
            log.error("停止视频流失败: deviceId={}, streamId={}", deviceId, streamId, e);
            return ResponseEntity.ok(Map.of(
                "success", false,
                "message", "停止视频流失败: " + e.getMessage(),
                "deviceId", deviceId,
                "streamId", streamId,
                "timestamp", System.currentTimeMillis()
            ));
        }
    }

    @Operation(summary = "升降台下降到取货口位", description = "发送指令让升降台下降到取货口位")
    @PostMapping("/{deviceId}/platform/lower-to-pickup")
    public ResponseEntity<Void> lowerPlatformToPickup(
            @Parameter(description = "设备ID", required = true, example = "drone-001")
            @PathVariable String deviceId) throws Exception {
        droneCabinService.sendControlCommand(deviceId, LiftPlatformCommand.lowerToPickup());
        return ResponseEntity.ok().build();
    }

    @Operation(summary = "升降台下降到送货口位", description = "发送指令让升降台下降到送货口位")
    @PostMapping("/{deviceId}/platform/lower-to-delivery")
    public ResponseEntity<Void> lowerPlatformToDelivery(
            @Parameter(description = "设备ID", required = true, example = "drone-001")
            @PathVariable String deviceId) throws Exception {
        droneCabinService.sendControlCommand(deviceId, LiftPlatformCommand.lowerToDelivery());
        return ResponseEntity.ok().build();
    }

    @Operation(summary = "升降台升到停机坪位", description = "发送指令让升降台升到停机坪位")
    @PostMapping("/{deviceId}/platform/rise-to-landing")
    public ResponseEntity<Void> risePlatformToLanding(
            @Parameter(description = "设备ID", required = true, example = "drone-001")
            @PathVariable String deviceId) throws Exception {
        droneCabinService.sendControlCommand(deviceId, LiftPlatformCommand.riseToLanding());
        return ResponseEntity.ok().build();
    }

    // ========== 货仓推杆控制 (action=108) ==========
    @Operation(summary = "推动货仓推杆", description = "发送指令推动货仓推杆")
    @PostMapping("/{deviceId}/cargo-pusher/push")
    public ResponseEntity<Void> pushCargoPusher(
            @Parameter(description = "设备ID", required = true, example = "drone-001")
            @PathVariable String deviceId) throws Exception {
        droneCabinService.sendControlCommand(deviceId, CargoPusherCommand.push());
        return ResponseEntity.ok().build();
    }

    @Operation(summary = "收回货仓推杆", description = "发送指令收回货仓推杆")
    @PostMapping("/{deviceId}/cargo-pusher/retract")
    public ResponseEntity<Void> retractCargoPusher(
            @Parameter(description = "设备ID", required = true, example = "drone-001")
            @PathVariable String deviceId) throws Exception {
        droneCabinService.sendControlCommand(deviceId, CargoPusherCommand.retract());
        return ResponseEntity.ok().build();
    }

    // ========== 上报周期设置 (action=109) ==========
    @Operation(summary = "设置状态上报周期", description = "设置无人机状态上报周期")
    @PostMapping("/{deviceId}/report-interval")
    public ResponseEntity<Void> setReportInterval(
            @Parameter(description = "设备ID", required = true, example = "drone-001")
            @PathVariable String deviceId,
            @Parameter(description = "上报周期(秒)", required = true, example = "60")
            @RequestParam int duration) throws Exception {
        droneCabinService.sendControlCommand(deviceId, ReportIntervalCommand.setInterval(duration));
        return ResponseEntity.ok().build();
    }

    // ========== 固件更新 (action=110) ==========
    @Operation(summary = "固件更新", description = "发送固件更新指令")
    @PostMapping("/{deviceId}/firmware/update")
    public ResponseEntity<Void> updateFirmware(
            @Parameter(description = "设备ID", required = true, example = "drone-001")
            @PathVariable String deviceId,
            @Parameter(description = "固件下载地址", required = true)
            @RequestParam String url,
            @Parameter(description = "固件版本", required = true)
            @RequestParam String version,
            @Parameter(description = "CRC32校验码", required = true)
            @RequestParam String checksum) throws Exception {
        droneCabinService.sendControlCommand(deviceId, FirmwareUpdateCommand.update(url, version, checksum));
        return ResponseEntity.ok().build();
    }

    // ========== 批量控制 (action=111) ==========
    @Operation(summary = "批量控制（协议标准格式）", description = "发送协议标准格式的批量控制指令")
    @PostMapping("/{deviceId}/batch/protocol")
    public ResponseEntity<Map<String, Object>> protocolBatchControl(
            @Parameter(description = "设备ID", required = true, example = "drone-001")
            @PathVariable String deviceId,
            @RequestBody BatchControlCommand batchCommand) {

        try {
            Map<String, Object> result = droneCabinService.executeBatchControl(deviceId, batchCommand);
            return ResponseEntity.ok(result);
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", e.getMessage(),
                "deviceId", deviceId,
                "timestamp", System.currentTimeMillis()
            ));
        } catch (Exception e) {
            return ResponseEntity.status(500).body(Map.of(
                "success", false,
                "message", "批量控制失败: " + e.getMessage(),
                "deviceId", deviceId,
                "timestamp", System.currentTimeMillis()
            ));
        }
    }

    @Operation(summary = "批量控制（扩展格式）", description = "发送扩展格式的批量控制指令（向后兼容）")
    @PostMapping("/{deviceId}/batch")
    public ResponseEntity<Void> batchControl(
            @Parameter(description = "设备ID", required = true, example = "drone-001")
            @PathVariable String deviceId,
            @RequestBody BatchControlCommand batchCommand) throws Exception {
        droneCabinService.sendControlCommand(deviceId, batchCommand);
        return ResponseEntity.ok().build();
    }

    // ========== 状态查询 ==========
    @Operation(summary = "获取状态", description = "获取无人机的当前状态")
    @GetMapping("/{deviceId}/status")
    public ResponseEntity<DeviceStatusResponse> getStatus(
            @Parameter(description = "设备ID", required = true, example = "drone-001")
            @PathVariable String deviceId) {
        DeviceStatusResponse status = droneCabinService.getDeviceStatus(deviceId);
        return ResponseEntity.ok(status);
    }

    // ========== 告警查询 ==========
    @Operation(summary = "获取告警信息", description = "获取无人机的告警信息")
    @GetMapping("/{deviceId}/warnings")
    public ResponseEntity<DeviceWarningsResponse> getWarnings(
            @Parameter(description = "设备ID", required = true, example = "drone-001")
            @PathVariable String deviceId) {
        DeviceWarningsResponse warnings = droneCabinService.getDeviceWarnings(deviceId);
        return ResponseEntity.ok(warnings);
    }

    // ========== 设备管理 ==========
    @Operation(summary = "获取在线设备列表", description = "获取当前在线的无人机设备列表")
    @GetMapping("/online")
    public ResponseEntity<List<String>> getOnlineDevices() {
        List<String> devices = droneCabinService.getOnlineDevices();
        return ResponseEntity.ok(devices);
    }

    @Operation(summary = "清除设备缓存", description = "清除指定设备的状态和告警缓存")
    @DeleteMapping("/{deviceId}/cache")
    public ResponseEntity<Void> clearDeviceCache(
            @Parameter(description = "设备ID", required = true, example = "drone-001")
            @PathVariable String deviceId) {
        droneCabinService.clearDeviceCache(deviceId);
        return ResponseEntity.ok().build();
    }





    /**
     * 获取有效的action编号列表
     * 使用BatchControlCommand中的常量
     */
    private Map<String, String> getValidActionNumbers() {
        Map<String, String> actions = new HashMap<>();
        actions.put(String.valueOf(BatchControlCommand.ActionType.CABIN_COVER), "舱盖控制");
        actions.put(String.valueOf(BatchControlCommand.ActionType.DRONE_LEVER), "无人机四方推杆控制");
        actions.put(String.valueOf(BatchControlCommand.ActionType.CARGO_PICKUP), "取货口控制");
        actions.put(String.valueOf(BatchControlCommand.ActionType.CARGO_DELIVERY), "送货口控制");
        actions.put(String.valueOf(BatchControlCommand.ActionType.WEIGHT_SCALE), "电子秤读取");
        actions.put(String.valueOf(BatchControlCommand.ActionType.VIDEO_STREAM), "视频监控");
        actions.put(String.valueOf(BatchControlCommand.ActionType.LIFT_PLATFORM), "升降台控制");
        actions.put(String.valueOf(BatchControlCommand.ActionType.CARGO_PUSHER), "货仓推杆控制");
        actions.put("109", "设置上报周期");
        actions.put("110", "固件更新");
        actions.put("111", "批量控制");
        return actions;
    }
}