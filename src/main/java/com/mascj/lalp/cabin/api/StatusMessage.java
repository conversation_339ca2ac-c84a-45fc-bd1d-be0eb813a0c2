package com.mascj.lalp.cabin.api;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Map;

/**
 * 状态反馈消息(action=201)
 */
@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
public class StatusMessage extends DroneCommand {
    public StatusMessage() {
        this.action = 201;
    }
    
    @JsonProperty("data")
    private Map<String, DeviceStatus> data;
    
    @JsonProperty("timestamp")
    private String timestamp;

    @Getter
    @Setter
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class DeviceStatus {
        @JsonProperty("status")
        private int status;
        @JsonProperty("duration")
        private int duration; // 状态持续时间，单位秒

        @JsonProperty("streams")
        private VideoStream[] streams;

        @Override
        public String toString() {
            StringBuilder sb = new StringBuilder();
            sb.append("DeviceStatus{");
            sb.append("status=").append(status);
            if (duration > 0) {
                sb.append(", duration=").append(duration);
            }
            if (streams != null && streams.length > 0) {
                sb.append(", streams=").append(java.util.Arrays.toString(streams));
            }
            sb.append("}");
            return sb.toString();
        }
    }

    @Getter
    @Setter
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class VideoStream {
        @JsonProperty("stream_id")
        private int streamId;

        @JsonProperty("rtsp_url")
        private String rtspUrl;

        @Override
        public String toString() {
            return "VideoStream{" +
                    "streamId=" + streamId +
                    ", rtspUrl='" + rtspUrl + '\'' +
                    '}';
        }
    }
}