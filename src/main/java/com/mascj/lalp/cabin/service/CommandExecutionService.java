package com.mascj.lalp.cabin.service;

import com.mascj.lalp.cabin.api.DroneCommand;
import com.mascj.lalp.cabin.api.CabinCoverCommand;
import com.mascj.lalp.cabin.dto.DeviceStatusResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * 指令执行服务
 * 负责发送指令后的状态检查和结果反馈
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CommandExecutionService {

    private final DroneCabinService droneCabinService;

    /**
     * 执行控制指令并检查状态
     * @param deviceId 设备ID
     * @param command 控制指令
     * @param actionName 动作名称
     * @param maxRetries 最大重试次数
     * @param delayMs 检查间隔（毫秒）
     * @return 执行结果
     */
    public Map<String, Object> executeCommandWithStatusCheck(
            String deviceId,
            DroneCommand command,
            String actionName,
            int maxRetries,
            long delayMs) {
        
        try {
            log.info("发送控制指令: deviceId={}, action={}", deviceId, actionName);
            
            // 1. 发送控制指令
            droneCabinService.sendControlCommand(deviceId, command);
            
            // 2. 检查指令执行状态
            DeviceStatusResponse finalStatus = checkCommandExecution(deviceId, maxRetries, delayMs);
            
            // 3. 构建响应
            return Map.of(
                "success", true,
                "message", actionName + "指令发送成功",
                "deviceId", deviceId,
                "action", actionName,
                "status", finalStatus,
                "timestamp", System.currentTimeMillis(),
                "checkAttempts", maxRetries
            );
            
        } catch (Exception e) {
            log.error("执行控制指令失败: deviceId={}, action={}", deviceId, actionName, e);
            return Map.of(
                "success", false,
                "message", actionName + "指令执行失败: " + e.getMessage(),
                "deviceId", deviceId,
                "action", actionName,
                "timestamp", System.currentTimeMillis(),
                "error", e.getMessage()
            );
        }
    }

    /**
     * 检查指令执行状态
     * @param deviceId 设备ID
     * @param maxRetries 最大重试次数
     * @param delayMs 检查间隔
     * @return 最终状态
     */
    private DeviceStatusResponse checkCommandExecution(String deviceId, int maxRetries, long delayMs) {
        DeviceStatusResponse status = null;
        
        for (int i = 0; i < maxRetries; i++) {
            try {
                // 等待设备处理指令
                Thread.sleep(delayMs);
                
                // 获取设备状态
                status = droneCabinService.getDeviceStatus(deviceId);
                
                log.debug("状态检查 {}/{}: deviceId={}, status={}", 
                    i + 1, maxRetries, deviceId, status);
                
                // 如果状态正常，可以提前结束检查
                if (isStatusStable(status)) {
                    log.info("设备状态稳定，提前结束检查: deviceId={}", deviceId);
                    break;
                }
                
            } catch (Exception e) {
                log.warn("状态检查失败 {}/{}: deviceId={}", i + 1, maxRetries, deviceId, e);
                if (i == maxRetries - 1) {
                    throw new RuntimeException("状态检查失败", e);
                }
            }
        }
        
        return status;
    }

    /**
     * 判断设备状态是否稳定
     * @param status 设备状态
     * @return 是否稳定
     */
    private boolean isStatusStable(DeviceStatusResponse status) {
        if (status == null) {
            return false;
        }
        
        // 根据具体的状态字段判断是否稳定
        // 这里需要根据您的实际状态结构来实现
        // 例如：检查货仓盖状态、设备连接状态等
        
        return true; // 暂时返回true，您可以根据实际需求修改
    }

    /**
     * 异步执行指令并检查状态
     * @param deviceId 设备ID
     * @param command 控制指令
     * @param actionName 动作名称
     * @return 异步结果
     */
    @Async
    public CompletableFuture<Map<String, Object>> executeCommandAsync(
            String deviceId,
            DroneCommand command,
            String actionName) {
        
        Map<String, Object> result = executeCommandWithStatusCheck(
            deviceId, command, actionName, 3, 500);
        
        return CompletableFuture.completedFuture(result);
    }

    /**
     * 快速执行指令（默认参数）
     */
    public Map<String, Object> executeCommand(String deviceId, DroneCommand command, String actionName) {
        return executeCommandWithStatusCheck(deviceId, command, actionName, 3, 500);
    }

    /**
     * 执行打开货仓盖指令
     */
    public Map<String, Object> executeOpenCover(String deviceId) {
        return executeCommand(deviceId, CabinCoverCommand.open(), "open_cover");
    }

    /**
     * 执行关闭货仓盖指令
     */
    public Map<String, Object> executeCloseCover(String deviceId) {
        return executeCommand(deviceId, CabinCoverCommand.close(), "close_cover");
    }
}
