package com.mascj.lalp.cabin.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.mascj.lalp.cabin.api.*;
import com.mascj.lalp.cabin.config.DeviceLogConfig;
import com.mascj.lalp.cabin.dto.DeviceStatusResponse;
import com.mascj.lalp.cabin.dto.DeviceWarningsResponse;
import com.mascj.lalp.cabin.util.DeviceStatusFormatter;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.paho.mqttv5.common.MqttException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 无人机座舱MQTT通信服务
 */
@Slf4j
@Service
public class DroneCabinService {

    @Autowired
    MqttGateway gateway;

    @Autowired
    private MqttConnectionMonitor connectionMonitor;

    @Autowired
    private DeviceLogConfig deviceLogConfig;

    private final ObjectMapper objectMapper = new ObjectMapper();

    // 设备状态缓存
    private final Map<String, Object> deviceStatusCache = new ConcurrentHashMap<>();

    // 设备上次状态缓存，用于状态变化检测
    private final Map<String, StatusMessage> lastStatusCache = new ConcurrentHashMap<>();

    // 设备告警缓存
    private final Map<String, List<WarningMessage>> deviceWarningsCache = new ConcurrentHashMap<>();

    // 设备状态变化时间记录（用于频繁变化检测）
    private final Map<String, LinkedList<LocalDateTime>> deviceChangeTimesCache = new ConcurrentHashMap<>();

    /**
     * 发送控制指令到指定设备
     *
     * @param deviceId 设备ID
     * @param command  控制指令
     */
    public void sendControlCommand(String deviceId, DroneCommand command) throws MqttException {
        try {
            // 检查连接状态
            if (!connectionMonitor.isConnected()) {
                log.warn("MQTT连接不可用，尝试重连...");
                connectionMonitor.forceReconnect();
                // 等待一小段时间让连接恢复
                Thread.sleep(1000);
            }

            String topic = String.format("/drone/cabin/%s/control", deviceId);
            String payload = command.toJson();
            gateway.sendToMqtt(payload, topic);

            log.info("已发送控制指令到设备 {}: {}", deviceId, command.toJson());
        } catch (Exception e) {
            log.error("发送控制指令失败: deviceId={}, command={}", deviceId, command.toJson(), e);
            connectionMonitor.recordConnectionError("发送指令失败: " + e.getMessage());
            throw new MqttException(e);
        }
    }

    /**
     * 处理MQTT消息
     * @param topic MQTT主题
     * @param payload 消息内容
     * @return 处理结果
     */
    public Object processMessage(String topic, String payload) {
        try {
            // 解析topic获取设备ID和消息类型
            String[] topicParts = topic.split("/");
            if (topicParts.length < 5) {
                log.warn("无效的topic格式: {}", topic);
                return null;
            }

            String deviceId = topicParts[3];
            String messageType = topicParts[4]; // status 或 warning

            // 只在DEBUG级别记录收到的消息，避免日志噪音
            log.debug("收到设备 {} 的 {} 消息: {}", deviceId, messageType, payload);

            if ("status".equals(messageType)) {
                return processStatusMessage(deviceId, payload);
            } else if ("warning".equals(messageType)) {
                return processWarningMessage(deviceId, payload);
            }

        } catch (Exception e) {
            log.error("处理MQTT消息失败: topic={}, payload={}", topic, payload, e);
        }

        return payload;
    }

    /**
     * 处理状态消息
     */
    private Object processStatusMessage(String deviceId, String payload) {
        try {
            // 清理可能的字符编码问题
            String cleanedPayload = cleanJsonPayload(payload);
            StatusMessage statusMessage = objectMapper.readValue(cleanedPayload, StatusMessage.class);

            // 缓存设备状态
            deviceStatusCache.put(deviceId, statusMessage);

            // 根据配置决定是否进行状态变化检测
            if (deviceLogConfig.isEnableChangeDetection()) {
                // 检查状态是否发生变化
                StatusMessage lastStatus = lastStatusCache.get(deviceId);
                boolean statusChanged = isStatusChanged(lastStatus, statusMessage);
                lastStatusCache.put(deviceId, statusMessage);

                if (statusChanged) {
                    // 状态发生变化时记录日志
                    String changes = lastStatus != null ? getStatusChanges(lastStatus, statusMessage) : "首次上报";
                    if (!changes.isEmpty() && !"首次上报".equals(changes)) {
                        // 检查是否是频繁变化（减少噪音）
                        if (isFrequentStatusChange(deviceId)) {
                            log.debug("设备 {} 状态变化（频繁）: {}", deviceId, changes);
                        } else {
                            log.info("设备 {} 状态变化: {}", deviceId, changes);
                        }
                    } else if (lastStatus == null) {
                        // 首次上报时记录完整状态
                        String formattedStatus = DeviceStatusFormatter.formatDeviceStatus(statusMessage.getData());
                        log.info("设备 {} 首次上报状态: {}", deviceId, formattedStatus);
                    }

                    // 检查是否有异常状态（仅在状态变化时记录）
                    if (DeviceStatusFormatter.hasAbnormalStatus(statusMessage.getData())) {
                        String abnormalDevices = DeviceStatusFormatter.getAbnormalDevices(statusMessage.getData());
                        log.warn("设备 {} 检测到异常状态: {}", deviceId, abnormalDevices);
                    }
                } else {
                    // 状态未变化时使用TRACE级别记录
                    log.trace("设备 {} 状态保持不变", deviceId);
                }
            } else {
                // 不启用变化检测时，记录所有状态消息（原有行为）
                String formattedStatus = DeviceStatusFormatter.formatDeviceStatus(statusMessage.getData());
                log.info("设备 {} 状态更新: {}", deviceId, formattedStatus);

                // 检查是否有异常状态
                if (DeviceStatusFormatter.hasAbnormalStatus(statusMessage.getData())) {
                    String abnormalDevices = DeviceStatusFormatter.getAbnormalDevices(statusMessage.getData());
                    log.warn("设备 {} 检测到异常状态: {}", deviceId, abnormalDevices);
                }
            }

            return statusMessage;
        } catch (Exception e) {
            log.error("解析状态消息失败: deviceId={}, payload={}", deviceId, payload, e);
            // 如果解析失败，直接缓存原始数据
            deviceStatusCache.put(deviceId, payload);
            return payload;
        }
    }

    /**
     * 处理告警消息
     */
    private Object processWarningMessage(String deviceId, String payload) {
        try {
            WarningMessage warningMessage = objectMapper.readValue(payload, WarningMessage.class);

            // 缓存告警信息
            deviceWarningsCache.computeIfAbsent(deviceId, k -> new ArrayList<>()).add(warningMessage);

            log.warn("设备 {} 产生告警: action={}, level={}",
                    deviceId, warningMessage.getAction(),
                    warningMessage.getData() != null ? warningMessage.getData().getLevel() : "unknown");

            return warningMessage;
        } catch (Exception e) {
            log.error("解析告警消息失败: deviceId={}, payload={}", deviceId, payload, e);
            return payload;
        }
    }

    /**
     * 获取设备状态
     * @param deviceId 设备ID
     * @return 设备状态
     */
    public DeviceStatusResponse getDeviceStatus(String deviceId) {
        Object status = deviceStatusCache.get(deviceId);
        if (status == null) {
            log.info("设备 {} 状态不存在，可能设备离线或未上报状态", deviceId);
            return DeviceStatusResponse.offline(deviceId);
        }

        Map<String, Object> statusData;
        if (status instanceof StatusMessage) {
            StatusMessage statusMessage = (StatusMessage) status;
            // 使用HashMap避免Map.of()的null值限制
            statusData = new HashMap<>();
            statusData.put("action", statusMessage.getAction()); // action是int类型，不会为null
            statusData.put("data", statusMessage.getData() != null ? statusMessage.getData() : new HashMap<>());
            statusData.put("timestamp", statusMessage.getTimestamp() != null ? statusMessage.getTimestamp() : "");
        } else {
            statusData = new HashMap<>();
            statusData.put("raw_data", status);
        }

        return DeviceStatusResponse.online(deviceId, statusData);
    }

    /**
     * 获取设备告警信息
     * @param deviceId 设备ID
     * @return 告警信息列表
     */
    public DeviceWarningsResponse getDeviceWarnings(String deviceId) {
        List<WarningMessage> warnings = deviceWarningsCache.get(deviceId);
        if (warnings == null) {
            warnings = new ArrayList<>();
        }
        return DeviceWarningsResponse.create(deviceId, warnings);
    }

    /**
     * 创建离线状态
     */
    private Object createOfflineStatus() {
        return Map.of(
            "status", "offline",
            "message", "设备离线或未上报状态",
            "timestamp", LocalDateTime.now().toString()
        );
    }

    /**
     * 清除设备缓存
     * @param deviceId 设备ID
     */
    public void clearDeviceCache(String deviceId) {
        deviceStatusCache.remove(deviceId);
        deviceWarningsCache.remove(deviceId);
        log.info("已清除设备 {} 的缓存数据", deviceId);
    }

    /**
     * 获取所有在线设备
     * @return 在线设备列表
     */
    public List<String> getOnlineDevices() {
        return new ArrayList<>(deviceStatusCache.keySet());
    }

    /**
     * 检查设备状态是否发生变化
     * @param lastStatus 上次状态
     * @param currentStatus 当前状态
     * @return 是否发生变化
     */
    private boolean isStatusChanged(StatusMessage lastStatus, StatusMessage currentStatus) {
        if (lastStatus == null) {
            return true; // 首次接收状态，认为是变化
        }

        if (currentStatus == null || currentStatus.getData() == null) {
            return false;
        }

        Map<String, StatusMessage.DeviceStatus> lastData = lastStatus.getData();
        Map<String, StatusMessage.DeviceStatus> currentData = currentStatus.getData();

        if (lastData == null) {
            return true;
        }

        // 检查设备数量是否变化
        if (lastData.size() != currentData.size()) {
            return true;
        }

        // 检查每个设备的状态是否变化
        for (Map.Entry<String, StatusMessage.DeviceStatus> entry : currentData.entrySet()) {
            String deviceId = entry.getKey();
            StatusMessage.DeviceStatus currentDeviceStatus = entry.getValue();
            StatusMessage.DeviceStatus lastDeviceStatus = lastData.get(deviceId);

            if (lastDeviceStatus == null) {
                return true; // 新设备
            }

            // 检查状态值是否变化
            if (lastDeviceStatus.getStatus() != currentDeviceStatus.getStatus()) {
                return true;
            }

            // 检查持续时间是否有显著变化（使用配置的阈值）
            if (Math.abs(lastDeviceStatus.getDuration() - currentDeviceStatus.getDuration()) > deviceLogConfig.getDurationChangeThreshold()) {
                return true;
            }
        }

        return false;
    }

    /**
     * 获取状态变化详情
     * @param lastStatus 上次状态
     * @param currentStatus 当前状态
     * @return 变化详情描述
     */
    private String getStatusChanges(StatusMessage lastStatus, StatusMessage currentStatus) {
        if (lastStatus == null || currentStatus == null) {
            return "";
        }

        Map<String, StatusMessage.DeviceStatus> lastData = lastStatus.getData();
        Map<String, StatusMessage.DeviceStatus> currentData = currentStatus.getData();

        if (lastData == null || currentData == null) {
            return "";
        }

        StringBuilder changes = new StringBuilder();

        // 检查每个设备的状态变化
        for (Map.Entry<String, StatusMessage.DeviceStatus> entry : currentData.entrySet()) {
            String deviceId = entry.getKey();
            StatusMessage.DeviceStatus currentDeviceStatus = entry.getValue();
            StatusMessage.DeviceStatus lastDeviceStatus = lastData.get(deviceId);

            if (lastDeviceStatus == null) {
                // 新增设备
                if (changes.length() > 0) changes.append(", ");
                changes.append(DeviceStatusFormatter.getDeviceDescription(deviceId)).append(":新增");
            } else if (lastDeviceStatus.getStatus() != currentDeviceStatus.getStatus()) {
                // 状态变化
                if (changes.length() > 0) changes.append(", ");
                String lastStatusDesc = DeviceStatusFormatter.getDeviceStatusDescription(deviceId, lastDeviceStatus.getStatus());
                String currentStatusDesc = DeviceStatusFormatter.getDeviceStatusDescription(deviceId, currentDeviceStatus.getStatus());
                changes.append(DeviceStatusFormatter.getDeviceDescription(deviceId))
                       .append(":").append(lastStatusDesc).append("→").append(currentStatusDesc);
            }
        }

        // 检查移除的设备
        for (String deviceId : lastData.keySet()) {
            if (!currentData.containsKey(deviceId)) {
                if (changes.length() > 0) changes.append(", ");
                changes.append(DeviceStatusFormatter.getDeviceDescription(deviceId)).append(":移除");
            }
        }

        return changes.toString();
    }

    /**
     * 检查设备是否频繁变化状态
     * @param deviceId 设备ID
     * @return 是否频繁变化
     */
    private boolean isFrequentStatusChange(String deviceId) {
        LocalDateTime now = LocalDateTime.now();
        LinkedList<LocalDateTime> changeTimes = deviceChangeTimesCache.computeIfAbsent(deviceId, k -> new LinkedList<>());

        // 记录当前变化时间
        changeTimes.add(now);

        // 清理5分钟前的记录
        changeTimes.removeIf(time -> time.isBefore(now.minusMinutes(5)));

        // 如果5分钟内变化超过10次，认为是频繁变化
        boolean isFrequent = changeTimes.size() > 10;

        if (isFrequent) {
            log.debug("设备 {} 在5分钟内状态变化 {} 次，判定为频繁变化", deviceId, changeTimes.size());
        }

        return isFrequent;
    }

    /**
     * 通用设备控制业务逻辑
     * 处理单个命令和批量命令的业务逻辑
     *
     * @param deviceId 设备ID
     * @param request 控制请求
     * @return 控制结果
     */
    public Map<String, Object> executeUniversalControl(String deviceId, Map<String, Object> request) {
        // 1. 参数解析和验证
        UniversalControlRequest controlRequest = parseAndValidateRequest(request);

        // 2. 创建命令
        DroneCommand command = createCommandFromRequest(controlRequest);

        // 3. 发送控制指令
        try {
            sendControlCommand(deviceId, command);
        } catch (MqttException e) {
            throw new RuntimeException("MQTT发送失败: " + e.getMessage(), e);
        }

        // 4. 构建成功响应
        return Map.of(
            "success", true,
            "message", "指令发送成功",
            "deviceId", deviceId,
            "action", controlRequest.getAction(),
            "timestamp", System.currentTimeMillis()
        );
    }

    /**
     * 解析和验证请求参数
     */
    private UniversalControlRequest parseAndValidateRequest(Map<String, Object> request) {
        // 获取action
        Object actionObj = request.get("action");
        if (actionObj == null) {
            throw new IllegalArgumentException("缺少必需的action参数");
        }

        int action = ((Number) actionObj).intValue();

        // 基本验证
        if (action < 101 || action > 111) {
            throw new IllegalArgumentException("无效的action编号: " + action + "，必须在101-111之间");
        }

        UniversalControlRequest controlRequest = new UniversalControlRequest();
        controlRequest.setAction(action);
        controlRequest.setRequest(request);

        return controlRequest;
    }

    /**
     * 根据请求创建命令
     */
    private DroneCommand createCommandFromRequest(UniversalControlRequest controlRequest) {
        int action = controlRequest.getAction();
        Map<String, Object> request = controlRequest.getRequest();

        if (action == 111) {
            // 批量控制命令
            return createBatchCommand(request);
        } else {
            // 单个命令
            return createSingleCommand(action, request);
        }
    }

    /**
     * 创建单个命令
     */
    private DroneCommand createSingleCommand(int action, Map<String, Object> request) {
        Object actionCodeObj = request.get("action_code");
        if (actionCodeObj == null) {
            throw new IllegalArgumentException("单个命令缺少action_code参数");
        }

        int actionCode = ((Number) actionCodeObj).intValue();
        return createCommandByActionAndCode(action, actionCode);
    }

    /**
     * 创建批量命令
     */
    private DroneCommand createBatchCommand(Map<String, Object> request) {
        Object dataObj = request.get("data");
        if (dataObj == null) {
            throw new IllegalArgumentException("批量命令缺少data参数");
        }

        BatchControlCommand batchCommand = new BatchControlCommand();
        batchCommand.setAction(111);
        batchCommand.setData(dataObj);

        return batchCommand;
    }

    /**
     * 根据action和actionCode创建具体命令
     */
    private DroneCommand createCommandByActionAndCode(int action, int actionCode) {
        switch (action) {
            case BatchControlCommand.ActionType.CABIN_COVER:
                return actionCode == BatchControlCommand.ActionCode.OPEN ?
                    CabinCoverCommand.open() : CabinCoverCommand.close();
            case BatchControlCommand.ActionType.DRONE_LEVER:
                return actionCode == BatchControlCommand.ActionCode.PUSH ?
                    DroneLeverCommand.push() : DroneLeverCommand.reset();
            case BatchControlCommand.ActionType.CARGO_PICKUP:
                return actionCode == BatchControlCommand.ActionCode.OPEN ?
                    CargoPickupCommand.open() : CargoPickupCommand.close();
            case BatchControlCommand.ActionType.CARGO_DELIVERY:
                return actionCode == BatchControlCommand.ActionCode.OPEN ?
                    CargoDeliveryCommand.open() : CargoDeliveryCommand.close();
            case BatchControlCommand.ActionType.WEIGHT_SCALE:
                return WeightScaleCommand.readWeight();
            case BatchControlCommand.ActionType.LIFT_PLATFORM:
                return createPlatformCommand(actionCode);
            case BatchControlCommand.ActionType.CARGO_PUSHER:
                return actionCode == BatchControlCommand.ActionCode.PUSH ?
                    CargoPusherCommand.push() : CargoPusherCommand.retract();
            default:
                throw new IllegalArgumentException("不支持的控制指令编号: " + action);
        }
    }

    /**
     * 创建升降台命令
     */
    private DroneCommand createPlatformCommand(int actionCode) {
        switch (actionCode) {
            case BatchControlCommand.ActionCode.PLATFORM_RESET:
                return LiftPlatformCommand.reset();
            case BatchControlCommand.ActionCode.RISE_TO_PICKUP:
                return LiftPlatformCommand.riseToPickup();
            case BatchControlCommand.ActionCode.RISE_TO_DELIVERY:
                return LiftPlatformCommand.riseToDelivery();
            case BatchControlCommand.ActionCode.LOWER_TO_PICKUP:
                return LiftPlatformCommand.lowerToPickup();
            case BatchControlCommand.ActionCode.LOWER_TO_DELIVERY:
                return LiftPlatformCommand.lowerToDelivery();
            case BatchControlCommand.ActionCode.RISE_TO_LANDING:
                return LiftPlatformCommand.riseToLanding();
            default:
                throw new IllegalArgumentException("不支持的升降台位置: " + actionCode);
        }
    }

    /**
     * 内部请求对象
     */
    private static class UniversalControlRequest {
        private int action;
        private Map<String, Object> request;

        public int getAction() { return action; }
        public void setAction(int action) { this.action = action; }
        public Map<String, Object> getRequest() { return request; }
        public void setRequest(Map<String, Object> request) { this.request = request; }
    }

    /**
     * 便捷设备控制业务逻辑
     * 供便捷接口使用的设备控制方法
     *
     * @param deviceId 设备ID
     * @param action 动作类型
     * @param actionCode 动作代码
     * @param withStatus 是否返回状态
     * @return 控制结果
     */
    public Map<String, Object> executeDeviceControl(String deviceId, String action, String actionCode, boolean withStatus) {
        log.info("设备控制: deviceId={}, action={}, actionCode={}, withStatus={}",
                deviceId, action, actionCode, withStatus);

        // 1. 创建命令
        DroneCommand command = createCommandByActionString(action, actionCode);

        // 2. 发送控制指令
        try {
            sendControlCommand(deviceId, command);
        } catch (MqttException e) {
            throw new RuntimeException("MQTT发送失败: " + e.getMessage(), e);
        }

        // 3. 构建基础响应
        Map<String, Object> result = Map.of(
            "success", true,
            "message", "指令发送成功",
            "deviceId", deviceId,
            "action", action + "_" + actionCode,
            "timestamp", System.currentTimeMillis()
        );

        // 4. 如果需要状态，获取设备状态
        if (withStatus) {
            try {
                // 等待一小段时间让设备处理指令
                Thread.sleep(500);
                // 获取设备状态
                DeviceStatusResponse status = getDeviceStatus(deviceId);
                result = Map.of(
                    "success", true,
                    "message", "指令发送成功",
                    "deviceId", deviceId,
                    "action", action + "_" + actionCode,
                    "status", status,
                    "timestamp", System.currentTimeMillis()
                );
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.warn("状态检查被中断: deviceId={}", deviceId);
            } catch (Exception e) {
                log.warn("获取设备状态失败: deviceId={}", deviceId, e);
            }
        }

        return result;
    }

    /**
     * 根据字符串action和actionCode创建命令
     * 复用Controller中原有的createCommand逻辑
     */
    private DroneCommand createCommandByActionString(String action, String actionCode) {
        switch (action.toLowerCase()) {
            case "cover":
            case "101":
                return "1".equals(actionCode) || "open".equals(actionCode) ?
                    CabinCoverCommand.open() : CabinCoverCommand.close();
            case "lever":
            case "102":
                return "1".equals(actionCode) || "push".equals(actionCode) ?
                    DroneLeverCommand.push() : DroneLeverCommand.reset();
            case "pickup":
            case "103":
                return "1".equals(actionCode) || "open".equals(actionCode) ?
                    CargoPickupCommand.open() : CargoPickupCommand.close();
            case "delivery":
            case "104":
                return "1".equals(actionCode) || "open".equals(actionCode) ?
                    CargoDeliveryCommand.open() : CargoDeliveryCommand.close();
            case "scale":
            case "105":
                return WeightScaleCommand.readWeight();
            case "video":
            case "106":
                // 视频流控制需要额外参数，这里返回基础命令
                return VideoStreamCommand.startStream(1, "");
            case "platform":
            case "107":
                return createPlatformCommandByString(actionCode);
            case "pusher":
            case "108":
                return "1".equals(actionCode) || "push".equals(actionCode) ?
                    CargoPusherCommand.push() : CargoPusherCommand.retract();
            default:
                throw new IllegalArgumentException("不支持的动作类型: " + action);
        }
    }

    /**
     * 根据字符串actionCode创建升降台命令
     */
    private DroneCommand createPlatformCommandByString(String actionCode) {
        int code = Integer.parseInt(actionCode != null ? actionCode : "1");
        switch (code) {
            case 1: return LiftPlatformCommand.reset();
            case 2: return LiftPlatformCommand.riseToPickup();
            case 3: return LiftPlatformCommand.riseToDelivery();
            case 4: return LiftPlatformCommand.lowerToPickup();
            case 5: return LiftPlatformCommand.lowerToDelivery();
            case 6: return LiftPlatformCommand.riseToLanding();
            default: throw new IllegalArgumentException("不支持的升降台位置: " + code);
        }
    }

    /**
     * 执行批量控制业务逻辑
     *
     * @param deviceId 设备ID
     * @param batchCommand 批量控制命令
     * @return 控制结果
     */
    public Map<String, Object> executeBatchControl(String deviceId, BatchControlCommand batchCommand) {
        // 1. 验证批量命令
        if (batchCommand == null) {
            throw new IllegalArgumentException("批量控制命令不能为空");
        }

        if (batchCommand.getAction() != 111) {
            throw new IllegalArgumentException("批量控制命令的action必须为111");
        }

        // 2. 发送控制指令
        try {
            sendControlCommand(deviceId, batchCommand);
        } catch (MqttException e) {
            throw new RuntimeException("MQTT发送失败: " + e.getMessage(), e);
        }

        // 3. 计算命令数量
        int commandCount = calculateCommandCount(batchCommand.getData());

        // 4. 构建响应
        return Map.of(
            "success", true,
            "message", "批量控制指令发送成功",
            "deviceId", deviceId,
            "commandCount", commandCount,
            "format", "protocol",
            "timestamp", System.currentTimeMillis()
        );
    }

    /**
     * 计算批量命令中的命令数量
     */
    private int calculateCommandCount(Object data) {
        if (data instanceof List) {
            return ((List<?>) data).size();
        } else if (data instanceof Map) {
            Map<?, ?> dataMap = (Map<?, ?>) data;
            Object commands = dataMap.get("commands");
            if (commands instanceof List) {
                return ((List<?>) commands).size();
            }
        }
        return 0;
    }

    /**
     * 清理JSON payload中的字符编码问题
     *
     * @param payload 原始JSON字符串
     * @return 清理后的JSON字符串
     */
    private String cleanJsonPayload(String payload) {
        if (payload == null || payload.trim().isEmpty()) {
            return payload;
        }

        try {
            // 1. 移除可能的BOM标记
            if (payload.startsWith("\uFEFF")) {
                payload = payload.substring(1);
            }

            // 2. 替换常见的乱码字段名
            // 将乱码的"st����"替换为"status"
            payload = payload.replaceAll("\"st[\\u0000-\\u001F\\uFFFD����]+\":", "\"status\":");

            // 3. 移除其他可能的控制字符
            payload = payload.replaceAll("[\\u0000-\\u001F\\u007F]", "");

            // 4. 确保JSON格式正确
            payload = payload.trim();

            log.debug("清理后的JSON payload: {}", payload);
            return payload;

        } catch (Exception e) {
            log.warn("清理JSON payload时发生异常，使用原始数据: {}", e.getMessage());
            return payload;
        }
    }
}