package com.mascj.lalp.cabin.config;

import org.springframework.amqp.support.converter.Jackson2JsonMessageConverter;
import org.springframework.amqp.support.converter.MessageConverter;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

/**
 * @Classname RabbitConfig
 * @Description TODO
 * @Version 1.0.0
 * @Date 2024/10/22 16:35
 * @Created by lionel
 */

@Component
public class RabbitConfig {

    @Bean
    public MessageConverter messageConverter() {
        return new Jackson2JsonMessageConverter();
    }
}
