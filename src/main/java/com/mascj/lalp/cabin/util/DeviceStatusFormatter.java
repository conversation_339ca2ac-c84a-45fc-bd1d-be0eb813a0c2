package com.mascj.lalp.cabin.util;

import com.mascj.lalp.cabin.api.StatusMessage;

import java.util.HashMap;
import java.util.Map;

/**
 * 设备状态格式化工具类
 * 用于将设备状态数据格式化为易读的字符串
 */
public class DeviceStatusFormatter {

    /**
     * 通用设备状态码含义映射（根据MQTT协议文档）
     * 注意：具体设备的状态含义由DEVICE_STATUS_DESCRIPTIONS定义
     */
    private static final Map<Integer, String> STATUS_DESCRIPTIONS = Map.of(
        0, "待机",
        1, "动作状态",  // 具体含义由设备类型决定
        2, "静止状态",  // 具体含义由设备类型决定
        3, "故障",
        4, "维护中",
        5, "待机"
    );

    /**
     * 设备ID含义映射（根据MQTT协议文档）
     */
    private static final Map<String, String> DEVICE_DESCRIPTIONS = Map.of(
        "101", "舱盖",
        "102", "无人机四方推杆",
        "103", "取货口",
        "104", "送货口",
        "105", "电子秤",
        "106", "视频监控",
        "107", "升降台",
        "108", "货仓推杆",
        "109", "上报周期设置"
    );

    /**
     * 动作编码含义映射（根据MQTT协议文档）
     */
    private static final Map<String, Map<Integer, String>> ACTION_CODE_DESCRIPTIONS = Map.of(
        "101", Map.of(1, "打开", 2, "关闭"),                    // 舱盖
        "102", Map.of(1, "推中", 2, "归位"),                    // 无人机四方推杆
        "103", Map.of(1, "开门", 2, "关门"),                    // 取货口
        "104", Map.of(1, "开门", 2, "关门"),                    // 送货口
        "105", Map.of(1, "读取重量"),                           // 电子秤
        "106", Map.of(1, "启动视频流", 2, "停止视频流"),         // 视频监控
        "107", Map.of(1, "归位", 2, "上升到取货口位", 3, "上升到送货口位",
                     4, "下降到取货口位", 5, "下降到送货口位", 6, "升到停机坪位"), // 升降台
        "108", Map.of(1, "推", 2, "收")                         // 货仓推杆
    );

    /**
     * 设备特定状态码映射（根据MQTT协议文档）
     */
    private static final Map<String, Map<Integer, String>> DEVICE_STATUS_DESCRIPTIONS;

    static {
        Map<String, Map<Integer, String>> deviceStatusMap = new HashMap<>();

        // 舱盖 (101)
        deviceStatusMap.put("101", Map.of(0, "待机", 1, "打开", 2, "关闭"));

        // 无人机四方推杆 (102)
        deviceStatusMap.put("102", Map.of(0, "待机", 1, "推中", 2, "归位"));

        // 取货口 (103)
        deviceStatusMap.put("103", Map.of(0, "待机", 1, "开门", 2, "关门"));

        // 送货口 (104)
        deviceStatusMap.put("104", Map.of(0, "待机", 1, "开门", 2, "关门"));

        // 电子秤 (105)
        deviceStatusMap.put("105", Map.of(0, "待机", 1, "正常"));

        // 视频监控 (106)
        deviceStatusMap.put("106", Map.of(0, "待机", 1, "正常"));

        // 升降台 (107) - 根据操作指令表
        Map<Integer, String> platformStatus = new HashMap<>();
        platformStatus.put(0, "待机");
        platformStatus.put(1, "归位");
        platformStatus.put(2, "上升到取货口位");
        platformStatus.put(3, "上升到送货口位");
        platformStatus.put(4, "下降到取货口位");
        platformStatus.put(5, "下降到送货口位");
        platformStatus.put(6, "升到停机坪位");
        deviceStatusMap.put("107", platformStatus);

        // 货仓推杆 (108)
        deviceStatusMap.put("108", Map.of(0, "待机", 1, "推", 2, "收"));

        DEVICE_STATUS_DESCRIPTIONS = Map.copyOf(deviceStatusMap);
    }

    /**
     * 格式化设备状态数据为易读字符串
     */
    public static String formatDeviceStatus(Map<String, StatusMessage.DeviceStatus> statusData) {
        if (statusData == null || statusData.isEmpty()) {
            return "无状态数据";
        }

        StringBuilder sb = new StringBuilder();
        statusData.forEach((deviceId, status) -> {
            if (sb.length() > 0) {
                sb.append(", ");
            }

            String deviceName = DEVICE_DESCRIPTIONS.getOrDefault(deviceId, "设备" + deviceId);
            String statusDesc = getDeviceStatusDescription(deviceId, status.getStatus());

            sb.append(deviceName).append(":").append(statusDesc);

            if (status.getDuration() > 0) {
                sb.append("(").append(status.getDuration()).append("s)");
            }
        });
        
        return sb.toString();
    }

    /**
     * 格式化单个设备状态
     */
    public static String formatSingleDeviceStatus(String deviceId, StatusMessage.DeviceStatus status) {
        String deviceName = DEVICE_DESCRIPTIONS.getOrDefault(deviceId, "设备" + deviceId);
        String statusDesc = STATUS_DESCRIPTIONS.getOrDefault(status.getStatus(), "未知状态(" + status.getStatus() + ")");
        
        StringBuilder sb = new StringBuilder();
        sb.append(deviceName).append(":").append(statusDesc);
        
        if (status.getDuration() > 0) {
            sb.append("(持续").append(status.getDuration()).append("秒)");
        }
        
        if (status.getStreams() != null && status.getStreams().length > 0) {
            sb.append(" [视频流:").append(status.getStreams().length).append("路]");
        }
        
        return sb.toString();
    }

    /**
     * 获取状态描述（通用版本，建议使用带设备ID的版本）
     */
    public static String getStatusDescription(int statusCode) {
        return STATUS_DESCRIPTIONS.getOrDefault(statusCode, "未知状态(" + statusCode + ")");
    }

    /**
     * 获取设备特定的状态描述
     */
    public static String getDeviceStatusDescription(String deviceId, int statusCode) {
        Map<Integer, String> deviceStatusMap = DEVICE_STATUS_DESCRIPTIONS.get(deviceId);
        if (deviceStatusMap != null) {
            return deviceStatusMap.getOrDefault(statusCode, "未知状态(" + statusCode + ")");
        }
        return STATUS_DESCRIPTIONS.getOrDefault(statusCode, "未知状态(" + statusCode + ")");
    }

    /**
     * 获取设备描述
     */
    public static String getDeviceDescription(String deviceId) {
        return DEVICE_DESCRIPTIONS.getOrDefault(deviceId, "设备" + deviceId);
    }

    /**
     * 检查是否有异常状态
     */
    public static boolean hasAbnormalStatus(Map<String, StatusMessage.DeviceStatus> statusData) {
        if (statusData == null) return false;

        return statusData.values().stream()
                .anyMatch(status -> status.getStatus() == 3 || status.getStatus() == 4); // 故障或维护中
    }

    /**
     * 获取异常设备列表
     */
    public static String getAbnormalDevices(Map<String, StatusMessage.DeviceStatus> statusData) {
        if (statusData == null) return "";

        StringBuilder sb = new StringBuilder();
        statusData.forEach((deviceId, status) -> {
            if (status.getStatus() == 3 || status.getStatus() == 4) { // 故障或维护中
                if (sb.length() > 0) sb.append(", ");
                sb.append(formatSingleDeviceStatus(deviceId, status));
            }
        });

        return sb.toString();
    }
}
