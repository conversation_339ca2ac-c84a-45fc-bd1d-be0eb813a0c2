package com.mascj.lalp.interfaces.rest.backend.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.mascj.lalp.domain.model.Drone;
import com.mascj.lalp.interfaces.rest.backend.dto.DronePageResponse;


public interface BackendDroneDeviceService extends IService<Drone> {

    /**
     * 搜索无人机设备（原有方法）
     * @param page 分页参数
     * @param keyword 搜索关键词
     * @param status 设备状态
     * @return 分页查询结果
     */
    Page<Drone> searchDrones(Page<Drone> page, String keyword, Integer status);

    /**
     * 分页查询无人机设备列表（包含业务逻辑处理）
     * @param pageNum 页码
     * @param pageSize 每页数量
     * @param keyword 搜索关键词
     * @param status 设备状态
     * @return 包含统计信息的分页响应
     */
    DronePageResponse listDroneDevices(int pageNum, int pageSize, String keyword, Integer status);
}
