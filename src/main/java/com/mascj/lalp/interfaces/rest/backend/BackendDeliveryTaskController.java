package com.mascj.lalp.interfaces.rest.backend;

import com.mascj.lalp.application.service.DeliveryTaskService;
import com.mascj.lalp.application.service.TaskFolderService;
import com.mascj.lalp.common.context.TenantContext;
import com.mascj.lalp.domain.model.DeliveryTask;
import com.mascj.lalp.infrastructure.common.api.ApiResult;
import com.mascj.lalp.infrastructure.common.api.PageResult;
import com.mascj.lalp.infrastructure.common.security.SecUtil;
import com.mascj.lalp.infrastructure.common.security.UserInfo;
import com.mascj.lalp.interfaces.rest.backend.dto.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 飞行任务管理控制器
 */
@Slf4j
@Tag(name = "后台-飞行任务管理", description = "飞行任务相关接口")
@RestController
@RequestMapping("/api/backend/delivery-tasks")
@RequiredArgsConstructor
public class BackendDeliveryTaskController {

    private final DeliveryTaskService deliveryTaskService;
    private final TaskFolderService taskFolderService;

    /**
     * 分页查询飞行任务列表
     *
     * @param query 查询参数
     * @return 分页飞行任务列表
     */
    @Operation(summary = "分页查询飞行任务列表")
    @GetMapping
    public ApiResult<PageResult<DeliveryPlanResponse>> listDeliveryTasks(
            DeliveryTaskPageQuery query, HttpServletRequest request) {
        log.info("查询飞行任务列表: {}", query);

        // 1. 调用服务层获取分页数据
        PageResult<DeliveryTask> taskPage = deliveryTaskService.listDeliveryTasksPage(query);

        // 2. 获取当前用户信息和收藏状态
        Map<Long, Boolean> favoriteStatusMap = getFavoriteStatusMap(taskPage.getRecords(), request);

        // 3. 转换为响应对象，包含收藏状态
        List<DeliveryPlanResponse> responseList = taskPage.getRecords().stream()
                .map(task -> {
                    Boolean favorited = favoriteStatusMap.get(task.getId());
                    return DeliveryPlanResponse.of(task, favorited);
                })
                .collect(Collectors.toList());

        // 4. 构建分页结果
        PageResult<DeliveryPlanResponse> pageResult = PageResult.of(
                taskPage.getCurrent(),
                taskPage.getSize(),
                taskPage.getTotal(),
                responseList);
        log.info("查询配送计划列表成功, total={}, records={}", taskPage.getTotal(), pageResult);
        return ApiResult.success(pageResult);
    }

    /**
     * 获取配送流程信息
     *
     * @param id 任务ID
     * @return 配送流程信息
     */
    @Operation(summary = "获取配送流程信息")
    @GetMapping("/{id}/process")
    public ApiResult<DeliveryProcessResponse> getDeliveryProcess(
            @Parameter(description = "任务ID", required = true) @PathVariable Long id) {

        log.info("获取配送流程信息: id={}", id);

        try {
            // 调用服务层获取配送流程信息
            DeliveryProcessResponse processResponse = deliveryTaskService.getDeliveryProcess(id);

            if (processResponse == null) {
                log.warn("配送任务不存在: id={}", id);
                return ApiResult.notFound("配送任务不存在");
            }

            log.info("成功获取配送流程信息: id={}, orderNo={}, currentStatus={}, nodesCount={}",
                    id, processResponse.getOrderNo(), processResponse.getCurrentStatus(),
                    processResponse.getProcessNodes() != null ? processResponse.getProcessNodes().size() : 0);

            return ApiResult.success(processResponse);

        } catch (Exception e) {
            log.error("获取配送流程信息失败: id={}", id, e);
            return ApiResult.serverError("获取配送流程信息失败: " + e.getMessage());
        }
    }

    /**
     * 获取飞行任务详情
     *
     * @param id 任务ID
     * @return 任务详情
     */
    @Operation(summary = "获取飞行任务详情")
    @GetMapping("/{id}")
    public ApiResult<DeliveryTask> getDeliveryTaskDetail(
            @Parameter(description = "任务ID", required = true) @PathVariable Long id) {

        log.info("获取飞行任务详情: id={}", id);

        try {
            // 从数据库查询真实的任务详情（包含订单信息）
            DeliveryTask task = deliveryTaskService.getTaskDetailWithOrder(id);

            if (task == null) {
                log.warn("飞行任务不存在: id={}", id);
                return ApiResult.notFound("飞行任务不存在");
            }

            log.info("成功获取飞行任务详情: id={}, status={}, droneId={}, orderNo={}, senderName={}",
                    id, task.getStatus(), task.getDroneId(),
                    task.getOrderId() != null ? "ORDER-" + task.getOrderId() : "无",
                    task.getCreatorName());

            return ApiResult.success(task);

        } catch (Exception e) {
            log.error("获取飞行任务详情失败: id={}", id, e);
            return ApiResult.serverError("获取飞行任务详情失败: " + e.getMessage());
        }
    }
 
    /**
     * 无人机就位
     *
     * @param taskId 任务ID
     * @return 任务详情
     */
    @Operation(summary = "无人机就位")
    @PostMapping("/{taskId}/drone-ready")
    public ApiResult<DeliveryTaskResponse> droneReady(
            @Parameter(description = "任务ID", required = true) @PathVariable Long taskId) {
        log.info("无人机就位, taskId={}", taskId);
        
        DeliveryTask task = deliveryTaskService.droneReady(taskId);
        return ApiResult.success(DeliveryTaskResponse.from(task));
    }

    /**
     * 标记已取货
     *
     * @param taskId 任务ID
     * @return 任务详情
     */
    @Operation(summary = "标记已取货")
    @PostMapping("/{taskId}/picked-up")
    public ApiResult<DeliveryTaskResponse> markAsPickedUp(
            @Parameter(description = "任务ID", required = true) @PathVariable Long taskId) {
        log.info("标记任务已取货, taskId={}", taskId);
        
        DeliveryTask task = deliveryTaskService.markAsPickedUp(taskId);
        return ApiResult.success(DeliveryTaskResponse.from(task));
    }
    
    /**
     * 标记配送中
     *
     * @param taskId 任务ID
     * @return 任务详情
     */
    @Operation(summary = "标记配送中")
    @PostMapping("/{taskId}/delivering")
    public ApiResult<DeliveryTaskResponse> markAsDelivering(
            @Parameter(description = "任务ID", required = true) @PathVariable Long taskId) {
        log.info("标记任务配送中, taskId={}", taskId);
        
        DeliveryTask task = deliveryTaskService.markAsDelivering(taskId);
        return ApiResult.success(DeliveryTaskResponse.from(task));
    }
    
    /**
     * 标记已到达目的地
     *
     * @param taskId 任务ID
     * @return 任务详情
     */
    @Operation(summary = "标记已到达目的地")
    @PostMapping("/{taskId}/arrived")
    public ApiResult<DeliveryTaskResponse> markAsArrived(
            @Parameter(description = "任务ID", required = true) @PathVariable Long taskId) {
        log.info("标记任务已到达目的地, taskId={}", taskId);
        
        DeliveryTask task = deliveryTaskService.markAsArrived(taskId);
        return ApiResult.success(DeliveryTaskResponse.from(task));
    }
    
    /**
     * 标记已投递
     *
     * @param taskId 任务ID
     * @return 任务详情
     */
    @Operation(summary = "标记已投递")
    @PostMapping("/{taskId}/delivered")
    public ApiResult<DeliveryTaskResponse> markAsDelivered(
            @Parameter(description = "任务ID", required = true) @PathVariable Long taskId) {
        log.info("标记任务已投递, taskId={}", taskId);
        
        DeliveryTask task = deliveryTaskService.markAsDelivered(taskId);
        return ApiResult.success(DeliveryTaskResponse.from(task));
    }

    /**
     * 日历统计接口：获取某月每天的配送计划数量
     * @param month 月份(yyyy-MM)
     * @return Map<String, DailyPlanCount>
     */
    @Operation(summary = "日历统计-获取某月每天的配送计划数量")
    @GetMapping("/calendar")
    public ApiResult<Map<String, DailyPlanCount>> getCalendarPlanCountByMonth(
            @Parameter(description = "月份，格式yyyy-MM", required = true) @RequestParam String month) {
        log.info("获取{}月的配送计划日历统计", month);
        Map<String, DailyPlanCount> result = deliveryTaskService.getCalendarPlanCountByMonth(month);
        return ApiResult.success(result);
    }

    // 创建模拟任务数据
    private DeliveryTaskResponse createMockTask(Long id, String senderName, String senderPhone,
                                              String recipientName, String recipientPhone, Integer status) {
        return DeliveryTaskResponse.builder()
                .id(id)
                .taskNumber("TASK" + String.format("%08d", id))
                .senderName(senderName)
                .senderPhone(senderPhone)
                .recipientName(recipientName)
                .recipientPhone(recipientPhone)
                .status(status)
                .takeoffTime(LocalDateTime.now().minusDays(1))
                .landingTime(LocalDateTime.now().plusHours(2))
                .returnTime(status == 2 ? LocalDateTime.now() : null)
                .build();
    }

    /**
     * 获取任务收藏状态映射
     * @param tasks 任务列表
     * @param request HTTP请求
     * @return 任务ID -> 是否收藏的映射
     */
    private Map<Long, Boolean> getFavoriteStatusMap(List<DeliveryTask> tasks, HttpServletRequest request) {
        try {
            // 获取当前用户信息
            UserInfo userInfo = SecUtil.getUserInfo(request);
            if (userInfo == null || userInfo.getAccountId() == null) {
                log.debug("用户未登录，返回空收藏状态");
                return tasks.stream().collect(Collectors.toMap(
                    DeliveryTask::getId,
                    task -> false
                ));
            }

            // 获取租户ID
            Long tenantId = TenantContext.getTenantId();
            if (tenantId == null) {
                log.debug("租户ID为空，返回空收藏状态");
                return tasks.stream().collect(Collectors.toMap(
                    DeliveryTask::getId,
                    task -> false
                ));
            }

            // 提取任务ID列表
            List<Long> taskIds = tasks.stream()
                .map(DeliveryTask::getId)
                .collect(Collectors.toList());

            // 批量查询收藏状态
            Map<Long, Boolean> favoriteStatusMap = taskFolderService.getBatchTaskFavoriteStatus(
                taskIds, userInfo.getAccountId(), tenantId);

            log.debug("查询收藏状态成功: userId={}, tenantId={}, taskCount={}, favoritedCount={}",
                userInfo.getAccountId(), tenantId, taskIds.size(),
                favoriteStatusMap.values().stream().mapToInt(b -> b ? 1 : 0).sum());

            return favoriteStatusMap;

        } catch (Exception e) {
            log.error("获取收藏状态失败", e);
            // 发生异常时返回默认状态（未收藏）
            return tasks.stream().collect(Collectors.toMap(
                DeliveryTask::getId,
                task -> false
            ));
        }
    }


}