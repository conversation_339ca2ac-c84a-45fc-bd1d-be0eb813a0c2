package com.mascj.lalp.interfaces.rest.backend.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * 通用响应基类
 * 统一所有飞控、设备控制等操作的响应格式
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "通用响应基类")
public class BaseFlightResponse {

    @Schema(description = "返回码", example = "200")
    private Integer code;

    @Schema(description = "返回信息", example = "处理成功")
    private String msg;

    @Schema(description = "时间戳", example = "1689751326084")
    @JsonProperty("timestamp")
    private Long time;

    @Schema(description = "成功状态", example = "true")
    private Boolean success;

    @Schema(description = "数据")
    private Object data;

    /**
     * 创建成功响应
     */
    public static BaseFlightResponse success(String message) {
        return BaseFlightResponse.builder()
                .code(200)
                .msg(message != null ? message : "处理成功")
                .time(System.currentTimeMillis())
                .success(true)
                .data(null)
                .build();
    }

    /**
     * 创建成功响应（带数据）
     */
    public static BaseFlightResponse success(String message, Object data) {
        return BaseFlightResponse.builder()
                .code(200)
                .msg(message != null ? message : "处理成功")
                .time(System.currentTimeMillis())
                .success(true)
                .data(data)
                .build();
    }

    /**
     * 创建失败响应
     */
    public static BaseFlightResponse failure(String message, Integer code) {
        return BaseFlightResponse.builder()
                .code(code != null ? code : 500)
                .msg(message != null ? message : "处理失败")
                .time(System.currentTimeMillis())
                .success(false)
                .data(null)
                .build();
    }

    /**
     * 创建失败响应（带错误代码）
     */
    public static BaseFlightResponse failure(String message, Integer code, String errorCode) {
        return BaseFlightResponse.builder()
                .code(code != null ? code : 500)
                .msg(message != null ? message : "处理失败")
                .time(System.currentTimeMillis())
                .success(false)
                .data(errorCode)
                .build();
    }
}
