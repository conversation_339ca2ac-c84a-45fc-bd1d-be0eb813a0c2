package com.mascj.lalp.interfaces.rest.backend.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDateTime;

import com.mascj.lalp.domain.model.DeliveryPlan;
import com.mascj.lalp.domain.model.DeliveryTask;
import com.mascj.lalp.domain.model.DeliveryTaskStatus;
import com.mascj.lalp.domain.model.CargoType;

/**
 * 配送计划请求DTO
 */
@Data
public class DeliveryPlanRequest {
    @Schema(description = "订单ID")
    private Long orderId;

    @Schema(description = "配送计划名称")
    @NotBlank(message = "配送计划名称不能为空")
    private String planName;
    /**
     * 发货点
     */
    @Schema(description = "发货点Code")
    @NotNull(message = "发货点Code不能为空")
    private String fromWarehouseCode;
    /**
     * 收获点
     */
    @Schema(description = "收货点Code")
    @NotNull(message = "收货点Code不能为空")
    private String recipientWarehouseCode;

    @Schema(description = "寄件人")
    @NotNull(message = "寄件人名字不能为空")
    private String senderName;

    @Schema(description = "寄件人手机号")
    @NotNull(message = "寄件人手机号不能为空")
    private String senderPhone;

    @Schema(description = "收件人")
    @NotNull(message = "收件人名字不能为空")
    private String receiverName;

    @Schema(description = "收件人手机号")
    @NotNull(message = "收件人手机号不能为空")
    private String receiverPhone;

    @Schema(description = "货物类型Code")
    @NotNull(message = "货物类型不能为空")
    private String cargoTypeCode;

    @Schema(description = "货物内容")
    @NotBlank(message = "货物内容不能为空")
    private String cargoContent;

    @Schema(description = "货物重量(kg)")
    @NotNull(message = "货物重量不能为空")
    private Double cargoWeight;
    //送货距离
    @Schema(description = "货物距离(m)")
    private Double deliveryDistance;
    //总飞行距离
    @Schema(description = "总飞行距离(m)")
    private Double totalFlightDistance;
    //总飞行时间
    @Schema(description = "总飞行时间(s)")
    private Integer totalFlightTime;
    @Schema(description = "返航时间(s)")
    private LocalDateTime returnTime;
    //签收时间
    @Schema(description = "签收时间(s)")
    private LocalDateTime receivedTime;
    //无人机编号
    @Schema(description = "无人机ID")
    private String droneId;

    //无人机编号（兼容前端字段）
    @Schema(description = "无人机编号（兼容字段）")
    private Integer aircraftCode;
    @Schema(description = "出发点")
    private String departurePoint;
    @Schema(description = "目的地")
    private String arrivalPoint;
    @Schema(description = "起飞时间")
    private LocalDateTime departureTime; // 起飞时间
    @Schema(description = "飞行高度(m)")
    private Double flightHeight; // 飞行高度
    @Schema(description = "送达时间")
    private LocalDateTime arrivalTime; // 送达时间
    @Schema(description = "配送计划")
    private DeliveryPlan deliveryPlan; // 配送计划
    @Schema(description = "货物类型")
    private String cargoType; // 货物类型名称
    private LocalDateTime deliveryTime; // 送货时间
    private Double flightDistance; // 飞行距离
    private DeliveryTaskStatus status; // 状态
    private String pickupCode; // 取货码
    private LocalDateTime createTime; // 创建时间
    private int deliveryDuration; // 送货时长
    private String creatorName; // 创建人
    private String creatorPhone; // 创建人手机号
    private Long tenantId; // 租户ID

    public DeliveryTask toDeliveryTask(CargoType cargoType) {
        DeliveryTask task = new DeliveryTask();
        // 设置订单ID
        task.setOrderId(this.getOrderId());
        // 设置无人机ID（优先使用droneId，aircraftCode需要在service层查询转换）
        task.setDroneId(this.getDroneId());
        // 设置发货点
        task.setDeparturePoint(this.getFromWarehouseCode());
        // 设置收货点
        task.setArrivalPoint(this.getRecipientWarehouseCode());
        // 初始时未起飞
        task.setDepartureTime(null);
        // 设置飞行高度，如果请求中没有指定则默认10米
        task.setFlightHeight(this.getFlightHeight() != null ? this.getFlightHeight() : 10.00);
        // 设置配送计划名称
        task.setPlanName(this.getPlanName());
        // 设置货物内容
        task.setCargoContent(this.getCargoContent());
        // 设置货物类型编码
        task.setCargoTypeCode(cargoType.getCode());
        // 设置货物类型名称
        task.setCargoType(cargoType.getName());
        // 设置货物重量
        task.setCargoWeight(this.getCargoWeight());
        // 设置收件人姓名
        task.setReceiverName(this.getReceiverName());
        // 设置收件人手机号
        task.setReceiverPhone(this.getReceiverPhone());
        // 初始状态为待处理
        task.setStatus(DeliveryTaskStatus.PENDING);
        // 设置签收时间
        task.setReceivedTime( this.getReceivedTime());
        // 设置创建人姓名（寄件人）
        task.setCreatorName(this.getSenderName());
        // 设置创建人手机号（寄件人）
        task.setCreatorPhone(this.getSenderPhone());
        //起飞时间
        task.setDepartureTime(this.getDepartureTime());
        //送达时间
        task.setArrivalTime(this.getArrivalTime());
        // 设置送货距离
        task.setDeliveryDistance(this.getDeliveryDistance());
        // 如果起飞时间和送达时间都存在，则计算总飞行时间
        if (task.getDepartureTime() != null && task.getArrivalTime() != null) {
            long durationSeconds = java.time.Duration.between(task.getDepartureTime(), task.getArrivalTime()).getSeconds();
            task.setTotalFlightTime((int) durationSeconds);
        }
        //返回时间
        task.setReturnTime(this.getReturnTime());
        //送货时间
        task.setDeliveryTime(this.getDeliveryTime());
        return task;
    }
}
