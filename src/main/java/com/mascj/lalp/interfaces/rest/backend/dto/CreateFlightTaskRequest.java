package com.mascj.lalp.interfaces.rest.backend.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.DecimalMax;
import jakarta.validation.constraints.DecimalMin;

/**
 * 创建飞行任务请求
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "创建飞行任务请求")
public class CreateFlightTaskRequest {

    @Schema(description = "设备SN", example = "BY71525020031", required = true)
    @NotBlank(message = "设备SN不能为空")
    private String deviceSn;

    @Schema(description = "任务名称", example = "测试", required = true)
    @NotBlank(message = "任务名称不能为空")
    private String name;

    @Schema(description = "起飞高度，保障两小数", example = "200", required = true)
    @NotNull(message = "起飞高度不能为空")
    @DecimalMin(value = "0", message = "起飞高度不能小于0")
    @DecimalMax(value = "500", message = "起飞高度不能超过500米")
    private Double alt;

    @Schema(description = "目标点物流仓SN", example = "TARGET_STORE_001", required = true)
    @NotBlank(message = "目标点物流仓SN不能为空")
    private String targetStoreSn;

    @Schema(description = "起飞点物流仓SN", example = "START_STORE_001", required = true)
    @NotBlank(message = "起飞点物流仓SN不能为空")
    private String startStoreSn;

    @Schema(description = "全局飞行速度（单位：m/s，默认：10）", example = "10")
    private String speed;

    @Schema(description = "航线完成时动作（0=返航；1=悬停；2=降落）", example = "2")
    private String finish;

    /**
     * 从配送任务创建飞行任务请求
     */
    public static CreateFlightTaskRequest fromDeliveryTask(
            com.mascj.lalp.domain.model.DeliveryTask task,
            String deviceSn,
            Double altitude) {

        return CreateFlightTaskRequest.builder()
                .deviceSn(deviceSn)
                .name(task.getPlanName() != null ? task.getPlanName() : "配送任务-" + task.getId())
                .alt(altitude != null ? altitude : 200.0) // 默认200米
                .targetStoreSn(task.getArrivalPoint())
                .startStoreSn(task.getDeparturePoint())
                .speed("12") // 默认12m/s
                .finish("2") // 默认降落
                .build();
    }
}
