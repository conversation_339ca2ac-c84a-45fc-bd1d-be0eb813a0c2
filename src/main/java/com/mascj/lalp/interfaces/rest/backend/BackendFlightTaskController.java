package com.mascj.lalp.interfaces.rest.backend;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mascj.lalp.application.service.FlightTaskService;
import com.mascj.lalp.domain.model.FlightTaskStatus;
import com.mascj.lalp.infrastructure.common.api.ApiResult;
import com.mascj.lalp.infrastructure.common.api.PageResult;
import com.mascj.lalp.interfaces.rest.backend.dto.FlightTaskResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 飞行任务管理控制器
 */
@Slf4j
@Tag(name = "后台-飞行任务管理", description = "飞行任务查询和管理接口")
@RestController
@RequestMapping("/api/backend/flight-task")
@RequiredArgsConstructor
public class BackendFlightTaskController {

    private final FlightTaskService flightTaskService;

    /**
     * 分页查询飞行任务
     */
    @Operation(summary = "分页查询飞行任务")
    @GetMapping("/page")
    public ApiResult<PageResult<FlightTaskResponse>> getFlightTaskPage(
            @Parameter(description = "页码", example = "1") @RequestParam(defaultValue = "1") int current,
            @Parameter(description = "每页大小", example = "10") @RequestParam(defaultValue = "10") int size,
            @Parameter(description = "设备SN") @RequestParam(required = false) String deviceSn,
            @Parameter(description = "任务状态") @RequestParam(required = false) String status,
            @Parameter(description = "开始时间") @RequestParam(required = false) 
            @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,
            @Parameter(description = "结束时间") @RequestParam(required = false) 
            @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime) {

        log.info("分页查询飞行任务: current={}, size={}, deviceSn={}, status={}, startTime={}, endTime={}",
                current, size, deviceSn, status, startTime, endTime);

        try {
            FlightTaskStatus taskStatus = null;
            if (status != null && !status.trim().isEmpty()) {
                taskStatus = FlightTaskStatus.fromString(status);
            }

            Page<FlightTaskResponse> page = flightTaskService.getFlightTaskPage(
                    current, size, deviceSn, taskStatus, startTime, endTime);

            PageResult<FlightTaskResponse> pageResult = PageResult.of(page);
            return ApiResult.success(pageResult);

        } catch (Exception e) {
            log.error("分页查询飞行任务失败", e);
            return ApiResult.serverError("查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据ID查询飞行任务详情
     */
    @Operation(summary = "查询飞行任务详情")
    @GetMapping("/{id}")
    public ApiResult<FlightTaskResponse> getFlightTaskById(
            @Parameter(description = "飞行任务ID", required = true) @PathVariable Long id) {

        log.info("查询飞行任务详情: id={}", id);

        try {
            FlightTaskResponse flightTask = flightTaskService.getFlightTaskById(id);
            if (flightTask == null) {
                return ApiResult.notFound("飞行任务不存在: " + id);
            }
            return ApiResult.success(flightTask);

        } catch (Exception e) {
            log.error("查询飞行任务详情失败: id={}", id, e);
            return ApiResult.serverError("查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据配送任务ID查询飞行任务
     */
    @Operation(summary = "根据配送任务ID查询飞行任务")
    @GetMapping("/delivery-task/{deliveryTaskId}")
    public ApiResult<List<FlightTaskResponse>> getFlightTasksByDeliveryTaskId(
            @Parameter(description = "配送任务ID", required = true) @PathVariable Long deliveryTaskId) {

        log.info("根据配送任务ID查询飞行任务: deliveryTaskId={}", deliveryTaskId);

        try {
            List<FlightTaskResponse> flightTasks = flightTaskService.getFlightTasksByDeliveryTaskId(deliveryTaskId);
            return ApiResult.success(flightTasks);

        } catch (Exception e) {
            log.error("根据配送任务ID查询飞行任务失败: deliveryTaskId={}", deliveryTaskId, e);
            return ApiResult.serverError("查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据设备SN查询飞行任务
     */
    @Operation(summary = "根据设备SN查询飞行任务")
    @GetMapping("/device/{deviceSn}")
    public ApiResult<List<FlightTaskResponse>> getFlightTasksByDeviceSn(
            @Parameter(description = "设备SN", required = true) @PathVariable String deviceSn) {

        log.info("根据设备SN查询飞行任务: deviceSn={}", deviceSn);

        try {
            List<FlightTaskResponse> flightTasks = flightTaskService.getFlightTasksByDeviceSn(deviceSn);
            return ApiResult.success(flightTasks);

        } catch (Exception e) {
            log.error("根据设备SN查询飞行任务失败: deviceSn={}", deviceSn, e);
            return ApiResult.serverError("查询失败: " + e.getMessage());
        }
    }

    /**
     * 查询正在执行的飞行任务
     */
    @Operation(summary = "查询正在执行的飞行任务")
    @GetMapping("/executing")
    public ApiResult<List<FlightTaskResponse>> getExecutingFlightTasks() {

        log.info("查询正在执行的飞行任务");

        try {
            List<FlightTaskResponse> flightTasks = flightTaskService.getExecutingFlightTasks();
            return ApiResult.success(flightTasks);

        } catch (Exception e) {
            log.error("查询正在执行的飞行任务失败", e);
            return ApiResult.serverError("查询失败: " + e.getMessage());
        }
    }

    /**
     * 飞行任务统计
     */
    @Operation(summary = "飞行任务统计")
    @GetMapping("/statistics")
    public ApiResult<Object> getFlightTaskStatistics() {

        log.info("查询飞行任务统计");

        try {
            Object statistics = flightTaskService.getFlightTaskStatistics();
            return ApiResult.success(statistics);

        } catch (Exception e) {
            log.error("查询飞行任务统计失败", e);
            return ApiResult.serverError("查询失败: " + e.getMessage());
        }
    }

    /**
     * 更新飞行任务状态
     */
    @Operation(summary = "更新飞行任务状态")
    @PutMapping("/{id}/status")
    public ApiResult<FlightTaskResponse> updateFlightTaskStatus(
            @Parameter(description = "飞行任务ID", required = true) @PathVariable Long id,
            @Parameter(description = "新状态", required = true) @RequestParam String status) {

        log.info("更新飞行任务状态: id={}, status={}", id, status);

        try {
            FlightTaskStatus taskStatus = FlightTaskStatus.fromString(status);
            FlightTaskResponse flightTask = flightTaskService.updateFlightTaskStatus(id, taskStatus);
            return ApiResult.success(flightTask);

        } catch (Exception e) {
            log.error("更新飞行任务状态失败: id={}, status={}", id, status, e);
            return ApiResult.serverError("更新失败: " + e.getMessage());
        }
    }
}
