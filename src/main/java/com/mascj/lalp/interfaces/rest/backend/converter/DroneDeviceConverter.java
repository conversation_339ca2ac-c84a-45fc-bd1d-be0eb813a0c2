package com.mascj.lalp.interfaces.rest.backend.converter;

import com.mascj.lalp.domain.model.DeviceType;
import com.mascj.lalp.domain.model.Drone;
import com.mascj.lalp.interfaces.rest.backend.dto.DroneDeviceResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;

/**
 * 无人机设备转换工具类
 * 负责实体与DTO之间的转换
 */
@Slf4j
@Component
public class DroneDeviceConverter {

    /**
     * 将Drone实体转换为DroneDeviceResponse
     * @param drone 无人机实体
     * @return 无人机设备响应DTO
     */
    public DroneDeviceResponse convertToResponse(Drone drone) {
        if (drone == null) {
            return null;
        }

        return DroneDeviceResponse.builder()
                .id(drone.getId())
                .name(drone.getName())
                .deviceNumber(drone.getDroneId())
                .missionCount(drone.getMissionCount())
                .currentLocation(drone.getLocation())
                .status(calculateStatus(drone))
                .deviceType(DeviceType.DRONE)
                .lastCommunicationTime(drone.getLastCommunicationTime())
                .batteryLevel(drone.getBatteryLevel())
                .online(isDroneOnline(drone))
                .build();
    }

    /**
     * 判断无人机是否在线
     * @param drone 无人机实体
     * @return 是否在线
     */
    public boolean isDroneOnline(Drone drone) {
        if (drone == null || drone.getLastCommunicationTime() == null) {
            return false;
        }

        long minutesSinceLastComm = ChronoUnit.MINUTES.between(
                drone.getLastCommunicationTime(),
                LocalDateTime.now()
        );

        return minutesSinceLastComm <= 5; // 5分钟内通信为在线
    }

    /**
     * 计算无人机状态
     * @param drone 无人机实体
     * @return 状态码 (0:离线, 1:在线, 2:任务中, 3:维护中)
     */
    public Integer calculateStatus(Drone drone) {
        if (drone == null) {
            return 0; // 离线
        }

        // 首先检查数据库中的状态字段
        String dbStatus = drone.getStatus();
        if (dbStatus != null) {
            switch (dbStatus.toUpperCase()) {
                case "ONLINE":
                    return isDroneOnline(drone) ? 1 : 0;
                case "OFFLINE":
                    return 0;
                case "WORKING":
                case "MISSION":
                    return 2;
                case "MAINTENANCE":
                    return 3;
                default:
                    break;
            }
        }

        // 如果数据库状态为空或未知，根据通信时间判断
        if (drone.getLastCommunicationTime() == null) {
            return 0; // 离线
        }
        
        long minutesSinceLastComm = ChronoUnit.MINUTES.between(
                drone.getLastCommunicationTime(), 
                LocalDateTime.now()
        );
        
        // 如果超过5分钟没有通信，认为离线
        if (minutesSinceLastComm > 5) {
            return 0; // 离线
        }
        
        // 这里可以添加其他状态判断逻辑
        // 例如检查是否在任务中等
        
        return 1; // 默认在线
    }

    /**
     * 统计在线无人机数量
     * @param drones 无人机列表
     * @return 在线数量
     */
    public long countOnlineDrones(java.util.List<Drone> drones) {
        if (drones == null || drones.isEmpty()) {
            return 0;
        }
        
        return drones.stream()
                .mapToLong(drone -> isDroneOnline(drone) ? 1 : 0)
                .sum();
    }

    /**
     * 统计离线无人机数量
     * @param drones 无人机列表
     * @return 离线数量
     */
    public long countOfflineDrones(java.util.List<Drone> drones) {
        if (drones == null || drones.isEmpty()) {
            return 0;
        }
        
        return drones.stream()
                .mapToLong(drone -> isDroneOnline(drone) ? 0 : 1)
                .sum();
    }
}
