package com.mascj.lalp.interfaces.rest.backend.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;

/**
 * 收藏任务到收藏夹请求DTO
 * 
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "收藏任务到收藏夹请求")
public class AddTaskToFolderRequest {

    @Schema(description = "任务ID", example = "106", required = true)
    @NotNull(message = "任务ID不能为空")
    @Positive(message = "任务ID必须大于0")
    private Long taskId;

    @Schema(description = "收藏夹ID（可选，不指定则收藏到默认收藏夹）", example = "6")
    @Positive(message = "收藏夹ID必须大于0")
    private Long folderId;

    @Schema(description = "收藏备注", example = "重要任务收藏")
    private String favoriteNote;

    @Schema(description = "是否快速收藏到默认收藏夹", example = "false")
    private Boolean quickFavorite;

    /**
     * 是否为快速收藏（收藏夹ID为空时自动判断）
     */
    public boolean isQuickFavorite() {
        return quickFavorite != null ? quickFavorite : (folderId == null);
    }
}
