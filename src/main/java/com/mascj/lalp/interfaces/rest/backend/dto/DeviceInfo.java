package com.mascj.lalp.interfaces.rest.backend.dto;

import com.mascj.lalp.domain.model.DeviceType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 设备信息DTO
 * 统一的设备信息表示，包含设备类型标识
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "设备信息")
public class DeviceInfo {
    
    @Schema(description = "设备ID")
    private Long id;
    
    @Schema(description = "设备名称")
    private String name;
    
    @Schema(description = "设备编号/代码")
    private String code;
    
    @Schema(description = "设备类型")
    private DeviceType deviceType;
    
    @Schema(description = "设备状态")
    private String status;
    
    @Schema(description = "设备位置/地址")
    private String location;
    
    @Schema(description = "创建时间")
    private LocalDateTime createTime;
    
    @Schema(description = "最后更新时间")
    private LocalDateTime lastUpdateTime;
    
    @Schema(description = "租户ID")
    private Long tenantId;
    
    @Schema(description = "扩展信息")
    private Object extendInfo;
}
