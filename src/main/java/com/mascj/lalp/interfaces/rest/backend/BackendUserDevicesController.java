package com.mascj.lalp.interfaces.rest.backend;

import com.mascj.lalp.application.service.UserDevicesService;
import com.mascj.lalp.infrastructure.common.api.ApiResult;
import com.mascj.lalp.interfaces.rest.backend.dto.UserDevicesResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@Tag(name = "后台-用户设备管理", description = "用户设备信息相关接口")
@RestController
@RequestMapping("/api/backend/user-devices")
@RequiredArgsConstructor
public class BackendUserDevicesController {

    private final UserDevicesService userDevicesService;

    /**
     * 查询当前用户的所有设备信息
     * 包括仓库、无人机和库存信息，以及设备类型标识和统计信息
     *
     * @return 用户的所有设备信息
     */
    @Operation(summary = "查询当前用户的所有设备信息")
    @GetMapping
    public ApiResult<UserDevicesResponse> getCurrentUserDevices() {
        log.info("查询当前用户的所有设备信息");

        UserDevicesResponse response = userDevicesService.getCurrentUserDevices();

        return ApiResult.success(response);
    }
}