package com.mascj.lalp.interfaces.rest.backend.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 创建飞行任务响应
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "创建飞行任务响应")
public class CreateFlightTaskResponse {

    @Schema(description = "是否成功", example = "true")
    private Boolean success;

    @Schema(description = "响应消息", example = "飞行任务创建成功")
    private String message;

    @Schema(description = "飞行任务ID", example = "FT20250730001")
    private String flightTaskId;

    @Schema(description = "设备SN", example = "BY71525020031")
    private String deviceSn;

    @Schema(description = "任务名称", example = "测试")
    private String taskName;

    @Schema(description = "创建时间戳", example = "1722326400000")
    private Long timestamp;

    @Schema(description = "错误代码（失败时）", example = "DEVICE_NOT_FOUND")
    private String errorCode;

    @Schema(description = "详细错误信息（失败时）")
    private String errorDetail;

    /**
     * 创建成功响应
     */
    public static CreateFlightTaskResponse success(String flightTaskId, String deviceSn, String taskName) {
        return CreateFlightTaskResponse.builder()
                .success(true)
                .message("飞行任务创建成功")
                .flightTaskId(flightTaskId)
                .deviceSn(deviceSn)
                .taskName(taskName)
                .timestamp(System.currentTimeMillis())
                .build();
    }

    /**
     * 创建失败响应
     */
    public static CreateFlightTaskResponse failure(String message, String errorCode, String errorDetail) {
        return CreateFlightTaskResponse.builder()
                .success(false)
                .message(message)
                .errorCode(errorCode)
                .errorDetail(errorDetail)
                .timestamp(System.currentTimeMillis())
                .build();
    }
}
