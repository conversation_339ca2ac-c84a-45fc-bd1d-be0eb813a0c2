package com.mascj.lalp.interfaces.rest.backend.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.HashMap;
import java.util.Map;

/**
 * 创建飞行任务响应
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Schema(description = "创建飞行任务响应")
public class CreateFlightTaskResponse extends BaseFlightResponse {

    @Schema(description = "飞行任务ID", example = "FT20250730001")
    private String flightTaskId;

    @Schema(description = "设备SN", example = "BY71525020031")
    private String deviceSn;

    @Schema(description = "任务名称", example = "测试")
    private String taskName;

    @Schema(description = "错误代码（失败时）", example = "DEVICE_NOT_FOUND")
    private String errorCode;

    @Schema(description = "详细错误信息（失败时）")
    private String errorDetail;

    /**
     * 创建成功响应
     */
    public static CreateFlightTaskResponse success(String flightTaskId, String deviceSn, String taskName) {
        // 构建数据对象
        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put("flightTaskId", flightTaskId);
        dataMap.put("deviceSn", deviceSn);
        dataMap.put("taskName", taskName);

        return CreateFlightTaskResponse.builder()
                .code(200)
                .msg("飞行任务创建成功")
                .time(System.currentTimeMillis())
                .success(true)
                .data(dataMap)
                .flightTaskId(flightTaskId)
                .deviceSn(deviceSn)
                .taskName(taskName)
                .build();
    }

    /**
     * 创建失败响应
     */
    public static CreateFlightTaskResponse failure(String message, String errorCode, String errorDetail) {
        // 构建错误数据对象
        Map<String, Object> errorData = new HashMap<>();
        errorData.put("errorCode", errorCode);
        errorData.put("errorDetail", errorDetail);

        return CreateFlightTaskResponse.builder()
                .code(500)
                .msg(message != null ? message : "飞行任务创建失败")
                .time(System.currentTimeMillis())
                .success(false)
                .data(errorData)
                .errorCode(errorCode)
                .errorDetail(errorDetail)
                .build();
    }

    /**
     * 获取成功状态（兼容旧接口）
     */
    public Boolean getSuccess() {
        return super.getSuccess();
    }

    /**
     * 获取消息（兼容旧接口）
     */
    public String getMessage() {
        return super.getMsg();
    }

    /**
     * 获取时间戳（兼容旧接口）
     */
    public Long getTimestamp() {
        return super.getTime();
    }
}
