package com.mascj.lalp.interfaces.rest.backend.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.mascj.lalp.infrastructure.common.jackson.EmptyStringToNullDeserializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * 远程飞控服务响应
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Schema(description = "远程飞控服务响应")
public class RemoteFlightTaskResponse extends BaseFlightResponse {

    @Schema(description = "飞行任务数据")
    @JsonDeserialize(using = EmptyStringToNullDeserializer.class)
    private FlightTaskData flightTaskData;

    /**
     * 重写data字段的getter，返回FlightTaskData
     */
    @Override
    public Object getData() {
        return this.flightTaskData;
    }

    /**
     * 重写data字段的setter，设置FlightTaskData
     */
    @Override
    public void setData(Object data) {
        if (data instanceof FlightTaskData) {
            this.flightTaskData = (FlightTaskData) data;
        }
        super.setData(data);
    }

    /**
     * 获取时间戳（兼容旧接口）
     */
    public Long getTimestamp() {
        return super.getTime();
    }

    /**
     * 设置时间戳（兼容旧接口）
     */
    @JsonProperty("timestamp")
    public void setTimestamp(Long timestamp) {
        super.setTime(timestamp);
    }

    /**
     * 飞行任务数据
     */
    @Data
    @lombok.Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "飞行任务数据")
    public static class FlightTaskData {
        
        @Schema(description = "任务ID", example = "1934857226446307330")
        private String id;

        @Schema(description = "作业ID", example = "5489bd2d-849d-45bb-9beb-f649c135e027")
        @JsonProperty("jobId")
        private String jobId;
    }

    /**
     * 转换为统一的响应格式
     */
    public CreateFlightTaskResponse toCreateFlightTaskResponse(String deviceSn, String taskName) {
        if (Boolean.TRUE.equals(this.success) && this.code == 200) {
            return CreateFlightTaskResponse.success(
                this.data != null ? this.data.getId() : null,
                deviceSn,
                taskName
            );
        } else {
            return CreateFlightTaskResponse.failure(
                this.msg != null ? this.msg : "未知错误",
                "REMOTE_ERROR_" + this.code,
                "远程服务返回错误: " + this.toString()
            );
        }
    }

    /**
     * 检查是否成功
     */
    public boolean isSuccess() {
        return Boolean.TRUE.equals(this.success) && this.code == 200;
    }
}
