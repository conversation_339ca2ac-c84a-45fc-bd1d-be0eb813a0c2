package com.mascj.lalp.interfaces.rest.backend.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.mascj.lalp.infrastructure.common.jackson.EmptyStringToNullDeserializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 远程飞控服务响应
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "远程飞控服务响应")
public class RemoteFlightTaskResponse {

    @Schema(description = "响应代码", example = "200")
    private Integer code;

    @Schema(description = "响应消息", example = "成功")
    private String msg;

    @Schema(description = "响应数据")
    @JsonDeserialize(using = EmptyStringToNullDeserializer.class)
    private FlightTaskData data;

    @Schema(description = "时间戳", example = "1750141021004")
    private Long timestamp;

    @Schema(description = "是否成功", example = "true")
    private Boolean success;

    /**
     * 飞行任务数据
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "飞行任务数据")
    public static class FlightTaskData {
        
        @Schema(description = "任务ID", example = "1934857226446307330")
        private String id;

        @Schema(description = "作业ID", example = "5489bd2d-849d-45bb-9beb-f649c135e027")
        @JsonProperty("jobId")
        private String jobId;
    }

    /**
     * 转换为统一的响应格式
     */
    public CreateFlightTaskResponse toCreateFlightTaskResponse(String deviceSn, String taskName) {
        if (Boolean.TRUE.equals(this.success) && this.code == 200) {
            return CreateFlightTaskResponse.success(
                this.data != null ? this.data.getId() : null,
                deviceSn,
                taskName
            );
        } else {
            return CreateFlightTaskResponse.failure(
                this.msg != null ? this.msg : "未知错误",
                "REMOTE_ERROR_" + this.code,
                "远程服务返回错误: " + this.toString()
            );
        }
    }

    /**
     * 检查是否成功
     */
    public boolean isSuccess() {
        return Boolean.TRUE.equals(this.success) && this.code == 200;
    }
}
