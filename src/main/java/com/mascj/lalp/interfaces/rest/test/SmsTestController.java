package com.mascj.lalp.interfaces.rest.test;

import com.mascj.lalp.application.service.sms.SmsService;
import com.mascj.lalp.infrastructure.common.api.ApiResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;

/**
 * 短信测试控制器
 * 用于测试短信发送功能
 */
@Slf4j
@RestController
@RequestMapping("/api/test/sms")
@RequiredArgsConstructor
@Tag(name = "短信测试", description = "短信发送功能测试接口")
public class SmsTestController {

    private final SmsService smsService;

    /**
     * 测试预约寄件短信
     */
    @PostMapping("/order-package")
    @Operation(summary = "测试预约寄件短信", description = "发送预约寄件短信到17681012481")
    public ApiResult<Map<String, Object>> testOrderPackageSms(
            @RequestParam(defaultValue = "123456") String senderCode) {
        
        log.info("测试预约寄件短信: senderCode={}", senderCode);
        
        try {
            // 使用真实用户数据
            Long tenantId = 1787645641420480513L; // 马超的租户ID
            List<Long> userIds = List.of(1943228955640770562L); // 马超的用户ID
            String phone = "17681012481"; // 马超的手机号
            
            // 发送预约寄件短信
            boolean success = smsService.sendOrderPackageSms(tenantId, userIds, senderCode);
            
            Map<String, Object> result = new HashMap<>();
            result.put("phone", phone);
            result.put("userName", "马超");
            result.put("senderCode", senderCode);
            result.put("tenantId", tenantId);
            result.put("userIds", userIds);
            result.put("success", success);
            result.put("templateType", "ORDER_PACKAGE");
            result.put("templateId", "SMS_492045119");
            result.put("message", success ? "预约寄件短信发送成功" : "预约寄件短信发送失败");
            result.put("smsContent", "尊敬的用户，您已成功预约寄件服务，寄件码" + senderCode + "，请尽快前往寄件。详情请登录靓马空投柜小程序查看");
            
            return ApiResult.success(result);
            
        } catch (Exception e) {
            log.error("测试预约寄件短信失败", e);
            return ApiResult.error(500, "发送失败: " + e.getMessage());
        }
    }

    /**
     * 测试包裹到达短信
     */
    @PostMapping("/package-arrived")
    @Operation(summary = "测试包裹到达短信", description = "发送包裹到达短信到17681012481")
    public ApiResult<Map<String, Object>> testPackageArrivedSms(
            @RequestParam(defaultValue = "789012") String pickupCode,
            @RequestParam(defaultValue = "北京朝阳物流中心") String warehouseName) {
        
        log.info("测试包裹到达短信: pickupCode={}, warehouseName={}", pickupCode, warehouseName);
        
        try {
            // 使用真实用户数据
            Long tenantId = 1787645641420480513L; // 马超的租户ID
            List<Long> userIds = List.of(1943228955640770562L); // 马超的用户ID
            String phone = "17681012481"; // 马超的手机号
            
            // 发送包裹到达短信
            boolean success = smsService.sendPackageArrivedSms(tenantId, userIds, warehouseName, pickupCode);
            
            Map<String, Object> result = new HashMap<>();
            result.put("phone", phone);
            result.put("userName", "马超");
            result.put("pickupCode", pickupCode);
            result.put("warehouseName", warehouseName);
            result.put("tenantId", tenantId);
            result.put("userIds", userIds);
            result.put("success", success);
            result.put("templateType", "PACKAGE_ARRIVED");
            result.put("templateId", "SMS_492035118");
            result.put("message", success ? "包裹到达短信发送成功" : "包裹到达短信发送失败");
            result.put("smsContent", "尊敬的用户，您有包裹到达" + warehouseName + "，取件码" + pickupCode + "，请尽快前往领取。详情请登录靓马空投柜小程序查看");
            
            return ApiResult.success(result);
            
        } catch (Exception e) {
            log.error("测试包裹到达短信失败", e);
            return ApiResult.error(500, "发送失败: " + e.getMessage());
        }
    }

    /**
     * 查看用户信息
     */
    @GetMapping("/user-info")
    @Operation(summary = "查看测试用户信息", description = "查看马超的用户信息")
    public ApiResult<Map<String, Object>> getUserInfo() {
        Map<String, Object> userInfo = new HashMap<>();
        
        userInfo.put("internalUserId", 19L); // 内部用户ID
        userInfo.put("outerUserId", 1943228955640770562L); // 外部用户ID (用于短信发送)
        userInfo.put("phone", "17681012481");
        userInfo.put("name", "马超");
        userInfo.put("status", "ENABLED");
        userInfo.put("type", "STANDARD");
        userInfo.put("tenantId", 1787645641420480513L);
        userInfo.put("message", "用户信息正常，短信发送使用外部用户ID: 1943228955640770562");
        
        return ApiResult.success(userInfo);
    }

    /**
     * 生成6位数字码
     */
    @GetMapping("/generate-code")
    @Operation(summary = "生成6位数字码", description = "生成随机的6位数字寄件码或取件码")
    public ApiResult<Map<String, Object>> generateCode(@RequestParam(defaultValue = "sender") String type) {
        String code = generateSixDigitCode();

        Map<String, Object> result = new HashMap<>();
        result.put("code", code);
        result.put("type", type);
        result.put("message", "生成6位数字" + ("sender".equals(type) ? "寄件码" : "取件码") + ": " + code);

        return ApiResult.success(result);
    }

    /**
     * 验证码格式
     */
    @PostMapping("/validate-code")
    @Operation(summary = "验证码格式", description = "验证寄件码或取件码是否为6位数字")
    public ApiResult<Map<String, Object>> validateCode(@RequestParam String code) {
        boolean isValid = isValidSixDigitCode(code);

        Map<String, Object> result = new HashMap<>();
        result.put("code", code);
        result.put("isValid", isValid);
        result.put("message", isValid ? "码格式正确" : "码格式错误，必须是6位数字");

        if (!isValid) {
            result.put("suggestion", "请使用6位数字，例如: " + generateSixDigitCode());
        }

        return ApiResult.success(result);
    }

    /**
     * 生成6位随机数字码
     */
    private String generateSixDigitCode() {
        Random random = new Random();
        int code = 100000 + random.nextInt(900000); // 生成100000-999999之间的数字
        return String.valueOf(code);
    }

    /**
     * 验证是否为6位数字码
     */
    private boolean isValidSixDigitCode(String code) {
        if (code == null || code.length() != 6) {
            return false;
        }

        try {
            Integer.parseInt(code);
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }
}
