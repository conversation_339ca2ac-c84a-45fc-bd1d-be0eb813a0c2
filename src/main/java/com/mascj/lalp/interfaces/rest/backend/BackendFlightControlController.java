package com.mascj.lalp.interfaces.rest.backend;

import com.mascj.lalp.application.service.DeliveryTaskService;
import com.mascj.lalp.application.service.FlightControlService;
import com.mascj.lalp.domain.model.DeliveryTask;
import com.mascj.lalp.infrastructure.common.api.ApiResult;
import com.mascj.lalp.interfaces.rest.backend.dto.CreateFlightTaskRequest;
import com.mascj.lalp.interfaces.rest.backend.dto.CreateFlightTaskResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 飞控系统管理控制器
 */
@Slf4j
@Tag(name = "后台-飞控系统管理", description = "飞控系统相关接口")
@RestController
@RequestMapping("/api/backend/flight-control")
@RequiredArgsConstructor
public class BackendFlightControlController {

    private final FlightControlService flightControlService;
    private final DeliveryTaskService deliveryTaskService;

    /**
     * 手动创建飞行任务
     *
     * @param request 创建飞行任务请求
     * @param authToken 认证令牌（可选）
     * @return 创建结果
     */
    @Operation(summary = "手动创建飞行任务")
    @PostMapping("/create-task")
    public ApiResult<CreateFlightTaskResponse> createFlightTask(
            @Validated @RequestBody CreateFlightTaskRequest request,
            @Parameter(description = "认证令牌") @RequestHeader(value = "liangma-auth", required = false) String authToken) {
        
        log.info("手动创建飞行任务: deviceSn={}, taskName={}", request.getDeviceSn(), request.getName());
        
        try {
            // 构建一个临时的配送任务对象用于测试
            DeliveryTask tempTask = new DeliveryTask();
            tempTask.setId(999L); // 测试用ID
            tempTask.setPlanName(request.getName());
            tempTask.setDroneId(request.getDeviceSn()); // 使用设备SN作为droneId
            tempTask.setDeparturePoint(request.getStartStoreSn());
            tempTask.setArrivalPoint(request.getTargetStoreSn());
            
            CreateFlightTaskResponse response = flightControlService.createFlightTaskForDelivery(tempTask, authToken);
            
            if (response.getSuccess()) {
                log.info("飞行任务创建成功: flightTaskId={}", response.getFlightTaskId());
                return ApiResult.success(response);
            } else {
                log.warn("飞行任务创建失败: {}", response.getMessage());
                return ApiResult.badRequest(response.getMessage());
            }
            
        } catch (Exception e) {
            log.error("创建飞行任务异常", e);
            return ApiResult.serverError("创建飞行任务失败: " + e.getMessage());
        }
    }

    /**
     * 为配送任务创建飞行任务
     *
     * @param taskId 配送任务ID
     * @param authToken 认证令牌（可选）
     * @return 创建结果
     */
    @Operation(summary = "为配送任务创建飞行任务")
    @PostMapping("/create-task-for-delivery/{taskId}")
    public ApiResult<CreateFlightTaskResponse> createFlightTaskForDelivery(
            @Parameter(description = "配送任务ID", required = true) @PathVariable Long taskId,
            @Parameter(description = "认证令牌") @RequestHeader(value = "liangma-auth", required = false) String authToken) {
        
        log.info("为配送任务创建飞行任务: taskId={}", taskId);
        
        try {
            // 获取配送任务
            DeliveryTask task = deliveryTaskService.getTaskDetail(taskId);
            if (task == null) {
                return ApiResult.notFound("配送任务不存在: " + taskId);
            }
            
            CreateFlightTaskResponse response = flightControlService.createFlightTaskForDelivery(task, authToken);
            
            if (response.getSuccess()) {
                log.info("飞行任务创建成功: taskId={}, flightTaskId={}", taskId, response.getFlightTaskId());
                return ApiResult.success(response);
            } else {
                log.warn("飞行任务创建失败: taskId={}, error={}", taskId, response.getMessage());
                return ApiResult.badRequest(response.getMessage());
            }
            
        } catch (Exception e) {
            log.error("为配送任务创建飞行任务异常: taskId={}", taskId, e);
            return ApiResult.serverError("创建飞行任务失败: " + e.getMessage());
        }
    }

    /**
     * 测试飞控接口连通性
     *
     * @param authToken 认证令牌（可选）
     * @return 测试结果
     */
    @Operation(summary = "测试飞控接口连通性")
    @PostMapping("/test-connection")
    public ApiResult<CreateFlightTaskResponse> testConnection(
            @Parameter(description = "认证令牌") @RequestHeader(value = "liangma-auth", required = false) String authToken) {
        
        log.info("测试飞控接口连通性");
        
        try {
            // 构建测试配送任务
            DeliveryTask testTask = new DeliveryTask();
            testTask.setId(0L);
            testTask.setPlanName("连通性测试");
            testTask.setDroneId("BY715225020031"); // 使用API文档中的设备SN
            testTask.setDeparturePoint("TEST_START");
            testTask.setArrivalPoint("TEST_TARGET");
            
            CreateFlightTaskResponse response = flightControlService.createFlightTaskForDelivery(testTask, authToken);
            
            log.info("飞控接口测试完成: success={}, message={}", response.getSuccess(), response.getMessage());
            return ApiResult.success(response);
            
        } catch (Exception e) {
            log.error("测试飞控接口异常", e);
            CreateFlightTaskResponse errorResponse = CreateFlightTaskResponse.failure(
                "连接测试失败: " + e.getMessage(),
                "CONNECTION_ERROR",
                e.toString()
            );
            return ApiResult.success(errorResponse);
        }
    }
}
