package com.mascj.lalp.interfaces.rest.backend.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.util.List;

/**
 * 无人机抓钩控制请求
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "无人机抓钩控制请求")
public class DroneHookControlRequest {

    @Schema(description = "博鹰飞机sn", example = "BY715225020031", required = true)
    @NotBlank(message = "设备SN不能为空")
    private String deviceSn;

    @Schema(description = "四个开关 0-开；1-关； 例如全开 [1,1,1,1]", example = "[1,1,1,1]", required = true)
    @NotNull(message = "开关状态列表不能为空")
    @Size(min = 4, max = 4, message = "开关状态列表必须包含4个元素")
    private List<Integer> indexList;
}
