package com.mascj.lalp.interfaces.rest.backend.service.Imp;

import com.alibaba.cloud.commons.lang.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mascj.lalp.common.util.QueryUtils;
import com.mascj.lalp.domain.model.Drone;
import com.mascj.lalp.domain.model.DroneStatus;
import com.mascj.lalp.interfaces.rest.backend.converter.DroneDeviceConverter;
import com.mascj.lalp.interfaces.rest.backend.dto.DroneDeviceResponse;
import com.mascj.lalp.interfaces.rest.backend.dto.DronePageResponse;
import com.mascj.lalp.interfaces.rest.backend.mapper.BackendDroneDeviceMapper;
import com.mascj.lalp.interfaces.rest.backend.service.BackendDroneDeviceService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 后台无人机设备服务实现
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class BackendDroneDeviceServiceImp extends ServiceImpl<BackendDroneDeviceMapper, Drone> implements BackendDroneDeviceService  {

    private final DroneDeviceConverter droneDeviceConverter;


    /**
     * 搜索无人机设备
     *
     * @param page 分页参数
     * @param keyword 搜索关键词（支持名称、设备ID、SIM卡号、设备序列号）
     * @param status 设备状态
     * @return 分页查询结果
     */
    @Override
    public Page<Drone> searchDrones(Page<Drone> page, String keyword, Integer status) {
        LambdaQueryWrapper<Drone> queryWrapper = new LambdaQueryWrapper<>();

        // 使用工具类构建嵌套关键词搜索条件
        QueryUtils.addNestedKeywordSearch(queryWrapper, keyword,
            Drone::getName, Drone::getDroneId, Drone::getSimCardNumber, Drone::getDeviceSn);

        // 添加状态条件
        if (status != null) {
            try {
                DroneStatus droneStatus = DroneStatus.fromCode(status);
                queryWrapper.eq(Drone::getStatus, droneStatus.name()); // 使用枚举名称匹配数据库字段
            } catch (IllegalArgumentException e) {
                log.warn("Invalid drone status code: {}", status, e);
            }
        }

        return page(page, queryWrapper);
    }

    /**
     * 分页查询无人机设备列表（包含业务逻辑处理）
     * @param pageNum 页码
     * @param pageSize 每页数量
     * @param keyword 搜索关键词
     * @param status 设备状态
     * @return 包含统计信息的分页响应
     */
    @Override
    public DronePageResponse listDroneDevices(int pageNum, int pageSize, String keyword, Integer status) {
        log.info("查询无人机设备列表: pageNum={}, pageSize={}, keyword={}, status={}",
                pageNum, pageSize, keyword, status);

        // 创建分页对象并执行分页查询
        Page<Drone> page = new Page<>(pageNum, pageSize);
        Page<Drone> dronePage = searchDrones(page, keyword, status);

        // 转换为响应DTO
        List<DroneDeviceResponse> deviceResponses = dronePage.getRecords().stream()
                .map(droneDeviceConverter::convertToResponse)
                .collect(Collectors.toList());

        // 统计在线/离线数量
        long onlineCount = droneDeviceConverter.countOnlineDrones(dronePage.getRecords());
        long offlineCount = droneDeviceConverter.countOfflineDrones(dronePage.getRecords());

        // 构建带统计信息的分页结果
        return DronePageResponse.builder()
                .list(deviceResponses)
                .pageNum(pageNum)
                .pageSize(pageSize)
                .total((int) dronePage.getTotal())
                .onlineCount(onlineCount)
                .offlineCount(offlineCount)
                .build();
    }
}
