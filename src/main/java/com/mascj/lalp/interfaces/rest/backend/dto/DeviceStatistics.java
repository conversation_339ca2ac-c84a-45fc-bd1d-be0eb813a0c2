package com.mascj.lalp.interfaces.rest.backend.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 设备统计信息
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "设备统计信息")
public class DeviceStatistics {
    
    @Schema(description = "仓库总数")
    private int totalWarehouses;
    
    @Schema(description = "在线仓库数")
    private int onlineWarehouses;
    
    @Schema(description = "离线仓库数")
    private int offlineWarehouses;
    
    @Schema(description = "无人机总数")
    private int totalDrones;
    
    @Schema(description = "在线无人机数")
    private int onlineDrones;
    
    @Schema(description = "离线无人机数")
    private int offlineDrones;
    
    @Schema(description = "任务中无人机数")
    private int workingDrones;
    
    @Schema(description = "维护中无人机数")
    private int maintenanceDrones;
    
    @Schema(description = "库存记录总数")
    private int totalInventories;
    
    @Schema(description = "设备总数")
    private int totalDevices;
}
