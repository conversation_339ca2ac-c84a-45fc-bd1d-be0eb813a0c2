package com.mascj.lalp.interfaces.rest.backend.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * 无人机抓钩控制响应
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Schema(description = "无人机抓钩控制响应")
public class DroneHookControlResponse extends BaseFlightResponse {

    /**
     * 创建成功响应
     */
    public static DroneHookControlResponse success(String deviceSn) {
        return DroneHookControlResponse.builder()
                .code(200)
                .msg("抓钩控制成功")
                .time(System.currentTimeMillis())
                .success(true)
                .data(deviceSn)
                .build();
    }

    /**
     * 创建失败响应
     */
    public static DroneHookControlResponse failure(String message, Integer code) {
        return DroneHookControlResponse.builder()
                .code(code != null ? code : 500)
                .msg(message != null ? message : "抓钩控制失败")
                .time(System.currentTimeMillis())
                .success(false)
                .data(null)
                .build();
    }
}
