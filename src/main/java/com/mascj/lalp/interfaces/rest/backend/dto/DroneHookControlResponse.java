package com.mascj.lalp.interfaces.rest.backend.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 无人机抓钩控制响应
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "无人机抓钩控制响应")
public class DroneHookControlResponse {

    @Schema(description = "返回码", example = "200")
    private Integer code;

    @Schema(description = "返回信息", example = "处理成功")
    private String msg;

    @Schema(description = "时间戳", example = "1689751326084")
    private Long time;

    @Schema(description = "成功状态", example = "true")
    private Boolean success;

    @Schema(description = "数据")
    private Object data;

    /**
     * 创建成功响应
     */
    public static DroneHookControlResponse success(String deviceSn) {
        return DroneHookControlResponse.builder()
                .code(200)
                .msg("处理成功")
                .time(System.currentTimeMillis())
                .success(true)
                .data(null)
                .build();
    }

    /**
     * 创建失败响应
     */
    public static DroneHookControlResponse failure(String message, Integer code) {
        return DroneHookControlResponse.builder()
                .code(code != null ? code : 500)
                .msg(message != null ? message : "处理失败")
                .time(System.currentTimeMillis())
                .success(false)
                .data(null)
                .build();
    }
}
