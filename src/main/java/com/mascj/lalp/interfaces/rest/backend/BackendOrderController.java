package com.mascj.lalp.interfaces.rest.backend;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.mascj.lalp.application.service.CargoTypeService;
import com.mascj.lalp.application.service.OrderService;
import com.mascj.lalp.application.service.UserService;
import com.mascj.lalp.domain.model.Order;
import com.mascj.lalp.infrastructure.common.api.ApiResult;
import com.mascj.lalp.infrastructure.common.api.PageResult;
import com.mascj.lalp.infrastructure.common.security.SecUtil;
import com.mascj.lalp.interfaces.rest.BaseOrderController;
import com.mascj.lalp.interfaces.rest.backend.dto.CreateOrderRequest;
import com.mascj.lalp.interfaces.rest.backend.dto.OrderPageQuery;
import com.mascj.lalp.interfaces.rest.backend.dto.OrderResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 后端订单管理接口（优化后继承基础类）
 *
 * 优化说明：
 * - 继承BaseOrderController，消除重复代码
 * - 保留后端特有的业务逻辑
 * - 统一错误处理和日志记录
 */
@Slf4j
@RestController
@RequestMapping("/api/backend/orders")
@Tag(name = "后台订单管理", description = "后端订单相关接口")
public class BackendOrderController extends BaseOrderController {

    private final UserService userService;

    public BackendOrderController(OrderService orderService, CargoTypeService cargoTypeService,
                                 UserService userService) {
        super(orderService, cargoTypeService);
        this.userService = userService;
    }

    /**
     * 分页查询订单列表（后端接口）
     */
    @Operation(summary = "分页查询订单列表")
    @GetMapping
    public ApiResult<PageResult<Order>> listOrders(@RequestHeader(SecUtil.LIANGMA_TOKEN) String token,
            @Validated OrderPageQuery query) {

        // 后端特有的token验证
        Long userId = userService.getCurrentUserId(token);
        if (userId == null) {
            return ApiResult.unauthorized("无效的认证信息");
        }
        // 后端特有的查询条件（按用户ID查询）
        query.setUserId(userId);
        // 调用基础类的统一查询逻辑
        try {
            IPage<Order> page = doListOrdersCore(query, "后端接口");
            return ApiResult.success(PageResult.of(page));
        } catch (Exception e) {
            logException(e, "分页查询订单", "后端接口");
            return ApiResult.serverError("查询订单失败: " + e.getMessage());
        }
    }

    /**
     * 获取订单详情（后端接口）
     */
    @Operation(summary = "获取订单详情")
    @GetMapping("/{id}")
    public ApiResult<OrderResponse> getOrderById(
            @Parameter(description = "订单ID", required = true) @PathVariable Long id) {

        // 调用基础类的统一查询逻辑
        try {
            Order order = doGetOrderByIdCore(id, "后端接口");
            if (order == null) {
                return com.mascj.lalp.infrastructure.common.api.ApiResult.notFound("订单不存在");
            }
            return com.mascj.lalp.infrastructure.common.api.ApiResult.success(OrderResponse.fromEntity(order));
        } catch (Exception e) {
            logException(e, "查询订单详情", "后端接口");
            return com.mascj.lalp.infrastructure.common.api.ApiResult.serverError("查询订单详情失败: " + e.getMessage());
        }
    }

    /**
     * 创建订单（后端接口）
     */
    @Operation(summary = "创建订单")
    @PostMapping
    public ApiResult<OrderResponse> createOrder(
            @RequestHeader(SecUtil.LIANGMA_TOKEN) String token,
            @Validated @RequestBody CreateOrderRequest request) {

        log.info("后端接口创建订单: token={}", token);

        // 后端特有的token验证
        Long userId = userService.getCurrentUserId(token);
        if (userId == null) {
            return ApiResult.unauthorized("无效的认证信息");
        }

        // 使用基础类的验证逻辑
        CreateOrderValidationResult validation = validateCreateOrderRequest(userId, request.getCargoType(), "后端接口");
        if (!validation.isSuccess()) {
            return com.mascj.lalp.infrastructure.common.api.ApiResult.notFound(validation.getErrorMessage());
        }

        try {
            // 后端特有的业务逻辑：创建普通订单
            Order order = orderService.createOrder(request.toOrder(userId, validation.getCargoType()));
            return com.mascj.lalp.infrastructure.common.api.ApiResult.success("创建订单成功", OrderResponse.fromEntity(order));
        } catch (Exception e) {
            logException(e, "创建订单", "后端接口");
            return com.mascj.lalp.infrastructure.common.api.ApiResult.serverError("创建订单失败: " + e.getMessage());
        }
    }

    /**
     * 标记订单已签收
     * @param orderId 订单ID
     * @return 订单详情
     */
    @Operation(summary = "标记订单已签收")
    @PostMapping("/{orderId}/received")
    public ApiResult<Order> markAsReceived(
            @Parameter(description = "订单ID", required = true) @PathVariable Long orderId) {
        try {
            log.info("标记订单已签收, orderId={}", orderId);
            Order order = orderService.markAsReceived(orderId);
            return ApiResult.success(order);
        } catch (Exception e) {
            log.error("标记订单已签收异常: ", e);
            return ApiResult.serverError(e.getMessage());
        }
    }
}
