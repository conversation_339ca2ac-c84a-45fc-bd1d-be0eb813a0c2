package com.mascj.lalp.interfaces.rest.backend.dto;

import com.mascj.lalp.domain.model.FlightTask;
import com.mascj.lalp.domain.model.FlightTaskStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 飞行任务响应DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "飞行任务响应")
public class FlightTaskResponse {

    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "飞控系统任务ID")
    private String flightTaskId;

    @Schema(description = "关联的配送任务ID")
    private Long deliveryTaskId;

    @Schema(description = "设备SN")
    private String deviceSn;

    @Schema(description = "任务名称")
    private String taskName;

    @Schema(description = "飞行高度（米）")
    private Double altitude;

    @Schema(description = "起飞点物流仓SN")
    private String startStoreSn;

    @Schema(description = "目标点物流仓SN")
    private String targetStoreSn;

    @Schema(description = "飞行速度（m/s）")
    private String speed;

    @Schema(description = "完成动作")
    private String finishAction;

    @Schema(description = "任务状态")
    private String status;

    @Schema(description = "状态描述")
    private String statusDesc;

    @Schema(description = "错误信息")
    private String errorMessage;

    @Schema(description = "错误代码")
    private String errorCode;

    @Schema(description = "创建人")
    private String createdBy;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "开始时间")
    private LocalDateTime startTime;

    @Schema(description = "结束时间")
    private LocalDateTime endTime;

    @Schema(description = "任务持续时间（分钟）")
    private Long duration;

    /**
     * 从FlightTask实体转换为响应DTO
     */
    public static FlightTaskResponse from(FlightTask flightTask) {
        if (flightTask == null) {
            return null;
        }

        // 计算任务持续时间
        Long duration = null;
        if (flightTask.getStartTime() != null && flightTask.getEndTime() != null) {
            duration = java.time.Duration.between(flightTask.getStartTime(), flightTask.getEndTime()).toMinutes();
        }

        return FlightTaskResponse.builder()
                .id(flightTask.getId())
                .flightTaskId(flightTask.getFlightTaskId())
                .deliveryTaskId(flightTask.getDeliveryTaskId())
                .deviceSn(flightTask.getDeviceSn())
                .taskName(flightTask.getTaskName())
                .altitude(flightTask.getAltitude())
                .startStoreSn(flightTask.getStartStoreSn())
                .targetStoreSn(flightTask.getTargetStoreSn())
                .speed(flightTask.getSpeed())
                .finishAction(flightTask.getFinishAction())
                .status(flightTask.getStatus() != null ? flightTask.getStatus().name() : null)
                .statusDesc(flightTask.getStatus() != null ? flightTask.getStatus().getDescription() : null)
                .errorMessage(flightTask.getErrorMessage())
                .errorCode(flightTask.getErrorCode())
                .createdBy(flightTask.getCreatedBy())
                .createTime(flightTask.getCreateTime())
                .updateTime(flightTask.getUpdateTime())
                .startTime(flightTask.getStartTime())
                .endTime(flightTask.getEndTime())
                .duration(duration)
                .build();
    }
}
