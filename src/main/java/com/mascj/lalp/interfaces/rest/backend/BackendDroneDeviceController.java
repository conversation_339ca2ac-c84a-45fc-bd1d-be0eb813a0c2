package com.mascj.lalp.interfaces.rest.backend;

import com.mascj.lalp.application.service.DroneService;
import com.mascj.lalp.domain.model.Drone;
import com.mascj.lalp.infrastructure.common.api.ApiResult;
import com.mascj.lalp.interfaces.rest.backend.dto.DronePageResponse;
import com.mascj.lalp.interfaces.rest.backend.service.BackendDroneDeviceService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 无人机设备管理控制器
 */
@Slf4j
@Tag(name = "后台-无人机设备管理", description = "无人机设备相关接口")
@RestController
@RequestMapping("/api/backend/drone-devices")
@RequiredArgsConstructor
public class BackendDroneDeviceController {

    @Autowired
    private BackendDroneDeviceService backendDroneDeviceService;

    private final DroneService droneService;

    /**
     * 分页查询无人机设备列表
     * 包括设备类型标识和统计信息
     *
     * @param pageNum  页码
     * @param pageSize 每页数量
     * @param keyword  搜索关键词（可搜索设备名称、编号或SN）
     * @param status   设备状态
     * @return 分页无人机设备列表
     */
    @Operation(summary = "分页查询无人机设备列表")
    @GetMapping
    public ApiResult<DronePageResponse> listDroneDevices(
            @Parameter(description = "页码", example = "1") @RequestParam(defaultValue = "1") int pageNum,
            @Parameter(description = "每页数量", example = "10") @RequestParam(defaultValue = "10") int pageSize,
            @Parameter(description = "搜索关键词（设备名称/编号/SN）") @RequestParam(required = false) String keyword,
            @Parameter(description = "设备状态(0:离线, 1:在线, 2:任务中, 3:维护中)") @RequestParam(required = false) Integer status) {

        log.info("查询无人机设备列表: pageNum={}, pageSize={}, keyword={}, status={}",
                pageNum, pageSize, keyword, status);

        DronePageResponse response = backendDroneDeviceService.listDroneDevices(pageNum, pageSize, keyword, status);

        return ApiResult.success(response);
    }


    /**
     * 获取无人机设备详情
     *
     * @param id 设备ID
     * @return 设备详情
     */
    @Operation(summary = "获取无人机设备详情")
    @GetMapping("/{id}")
    public ApiResult<Drone> getDroneDeviceDetail(
            @Parameter(description = "设备ID", required = true) @PathVariable Long id) { 
        log.info("获取无人机设备详情: id={}", id);
        Drone drone = droneService.getDroneById(id); 
        return ApiResult.success(drone);
    }
}
