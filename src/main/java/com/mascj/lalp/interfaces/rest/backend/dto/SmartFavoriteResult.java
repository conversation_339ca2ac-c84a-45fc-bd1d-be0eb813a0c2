package com.mascj.lalp.interfaces.rest.backend.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 智能收藏操作结果DTO
 * 
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "智能收藏操作结果")
public class SmartFavoriteResult {

    @Schema(description = "任务ID", example = "106")
    private Long taskId;

    @Schema(description = "用户ID", example = "1001")
    private Long userId;

    @Schema(description = "操作类型", example = "favorite", allowableValues = {"favorite", "unfavorite"})
    private String action;

    @Schema(description = "当前收藏状态", example = "true")
    private Boolean favorited;

    @Schema(description = "操作消息", example = "收藏成功")
    private String message;

    @Schema(description = "收藏夹ID（收藏操作时返回）", example = "1")
    private Long folderId;

    @Schema(description = "收藏夹名称（收藏操作时返回）", example = "我的收藏")
    private String folderName;

    @Schema(description = "移除的收藏数量（取消收藏操作时返回）", example = "2")
    private Integer removedCount;

    @Schema(description = "原始收藏数量（取消收藏操作时返回）", example = "2")
    private Integer originalFavoriteCount;

    @Schema(description = "操作时间戳", example = "1722326400000")
    private Long timestamp;

    /**
     * 创建收藏成功结果
     */
    public static SmartFavoriteResult favoriteSuccess(Long taskId, Long userId, Long folderId, 
                                                     String folderName, Long timestamp) {
        return SmartFavoriteResult.builder()
                .taskId(taskId)
                .userId(userId)
                .action("favorite")
                .favorited(true)
                .message("收藏成功")
                .folderId(folderId)
                .folderName(folderName)
                .timestamp(timestamp)
                .build();
    }

    /**
     * 创建取消收藏成功结果
     */
    public static SmartFavoriteResult unfavoriteSuccess(Long taskId, Long userId, 
                                                       Integer removedCount, Integer originalCount,
                                                       Long timestamp) {
        return SmartFavoriteResult.builder()
                .taskId(taskId)
                .userId(userId)
                .action("unfavorite")
                .favorited(false)
                .message("取消收藏成功")
                .removedCount(removedCount)
                .originalFavoriteCount(originalCount)
                .timestamp(timestamp)
                .build();
    }
}
