package com.mascj.lalp.interfaces.rest.backend;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mascj.lalp.application.service.SenderService;
import com.mascj.lalp.application.service.RecipientService;
import com.mascj.lalp.common.util.PageUtils;
import com.mascj.lalp.domain.model.Sender;
import com.mascj.lalp.domain.model.Recipient;
import com.mascj.lalp.infrastructure.common.api.ApiResult;
import com.mascj.lalp.infrastructure.common.api.PageResult;
import com.mascj.lalp.interfaces.rest.backend.dto.SenderResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 寄件人管理控制器
 */
@Slf4j
@Tag(name = "后台-寄件人管理", description = "寄件人相关接口")
@RestController
@RequestMapping("/api/backend/senders")
@RequiredArgsConstructor
public class BackendSenderController {

    private final SenderService senderService;
    private final RecipientService recipientService;

    /**
     * 分页查询寄件人列表（支持模糊搜索）
     *
     * @param pageNum  页码
     * @param pageSize 每页数量
     * @param keyword  搜索关键词（可搜索姓名或手机号）
     * @return 分页寄件人列表
     */
    @Operation(summary = "分页查询寄件人列表（支持模糊搜索）")
    @GetMapping
    public ApiResult<PageResult<SenderResponse>> listSenders(
            @Parameter(description = "页码", example = "1") @RequestParam(defaultValue = "1") int pageNum,
            @Parameter(description = "每页数量", example = "10") @RequestParam(defaultValue = "10") int pageSize,
            @Parameter(description = "搜索关键词（姓名或手机号）") @RequestParam(required = false) String keyword) {

        log.info("查询寄件人列表: pageNum={}, pageSize={}, keyword={}", pageNum, pageSize, keyword);

        // 使用Service层进行数据库分页查询
        Page<Sender> senderPage = senderService.pageSenders(pageNum, pageSize, keyword);

        // 使用工具类转换分页结果，并转换实体为响应DTO
        PageResult<SenderResponse> result = PageUtils.toPageResult(senderPage, this::convertToResponse);

        return ApiResult.success(result);
    }

    /**
     * 将Sender实体转换为SenderResponse
     *
     * @param sender 寄件人实体
     * @return 寄件人响应DTO
     */
    private SenderResponse convertToResponse(Sender sender) {
        return SenderResponse.builder()
                .id(sender.getId())
                .name(sender.getName())
                .phone(sender.getPhone())
                .shipmentCount(sender.getDeliveryCount())
                .latestTrackingCargo(sender.getRecentDeliveryItem())
                .latestShipmentTime(sender.getLastDeliveryTime())
                .createTime(sender.getCreateTime())
                .build();
    }

    /**
     * 统一联系人自动补全（同时查询寄件人和收件人）
     * Controller保持简洁，业务逻辑在Service层
     */
    @Operation(summary = "统一联系人自动补全",
               description = "同时查询寄件人和收件人表，根据输入的姓名或手机号提供联系人建议，按使用频率排序")
    @GetMapping("/unified-suggestions")
    public ApiResult<List<Map<String, Object>>> getUnifiedContactSuggestions(
            @Parameter(description = "搜索关键词（姓名或手机号）", example = "张")
            @RequestParam String keyword,
            @Parameter(description = "返回数量限制", example = "10")
            @RequestParam(defaultValue = "10") int limit) {

        log.info("获取统一联系人自动补全建议: keyword={}, limit={}", keyword, limit);
        List<Map<String, Object>> result = senderService.getUnifiedContactSuggestions(keyword, limit);
        return ApiResult.success(result);
    }
}
