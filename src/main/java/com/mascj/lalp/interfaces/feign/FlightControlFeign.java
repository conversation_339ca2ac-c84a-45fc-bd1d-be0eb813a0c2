package com.mascj.lalp.interfaces.feign;

import com.mascj.lalp.interfaces.rest.backend.dto.CreateFlightTaskRequest;
import com.mascj.lalp.interfaces.rest.backend.dto.DroneHookControlRequest;
import com.mascj.lalp.interfaces.rest.backend.dto.RemoteFlightTaskResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

/**
 * 飞控系统远程接口
 */
@FeignClient(
    name = "flightControlClient",
    url = "${feign.client.flight-control.url:https://unit.xiaoliangma.com}"
)
public interface FlightControlFeign {

    /**
     * 创建飞行任务（返回原始字符串）
     *
     * @param authToken 认证令牌
     * @param request 创建飞行任务请求
     * @return 原始响应字符串
     */
    @PostMapping("/api/lup-cds-djicloud/v1/wayline-job/by/create-flight-task")
    String createFlightTask(
            @RequestHeader("liangma-auth") String authToken,
            @RequestBody CreateFlightTaskRequest request
    );

    /**
     * 无人机抓钩控制（返回原始字符串）
     *
     * @param authToken 认证令牌
     * @param request 抓钩控制请求
     * @return 原始响应字符串
     */
    @PostMapping("/api/lup-cds-djicloud/v1/device-control/by/throw-and-grab")
    String droneHookControl(
            @RequestHeader("liangma-auth") String authToken,
            @RequestBody DroneHookControlRequest request
    );
}
