package com.mascj.lalp.domain.model;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;

/**
 * 寄件人信息
 */
@Data
@TableName("lalp_sender")
public class Sender {
    @TableId
    private Long id;
    private String name;
    private String phone;
    private Integer deliveryCount;
    private String recentDeliveryItem;
    private LocalDateTime lastDeliveryTime;
    private LocalDateTime createTime;
    private Long tenantId;

    /**
     * 转换为统一联系人Map
     */
    public Map<String, Object> toUnifiedContactMap() {
        Map<String, Object> contact = new HashMap<>();
        contact.put("id", this.id);
        contact.put("name", this.name);
        contact.put("phone", this.phone);
        contact.put("senderCount", this.deliveryCount);
        contact.put("recipientCount", 0);
        contact.put("totalUsageCount", this.deliveryCount);
        contact.put("recentSentItem", this.recentDeliveryItem);
        contact.put("recentReceivedItem", null);
        contact.put("displayText", this.name + " - " + this.phone);
        contact.put("contactType", "sender");
        contact.put("lastUsedTime", this.lastDeliveryTime);
        return contact;
    }
}