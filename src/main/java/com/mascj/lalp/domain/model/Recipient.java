package com.mascj.lalp.domain.model;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;

/**
 * 收件人信息
 */
@Data
@TableName("lalp_recipient")
public class Recipient {
    @TableId
    private Long id;
    private String name;
    private String phone;
    private Integer receiveCount;
    private String recentReceivedItem;
    private LocalDateTime lastReceiveTime;
    private Long tenantId;

    /**
     * 转换为统一联系人Map
     */
    public Map<String, Object> toUnifiedContactMap() {
        Map<String, Object> contact = new HashMap<>();
        contact.put("id", this.id);
        contact.put("name", this.name);
        contact.put("phone", this.phone);
        contact.put("senderCount", 0);
        contact.put("recipientCount", this.receiveCount);
        contact.put("totalUsageCount", this.receiveCount);
        contact.put("recentSentItem", null);
        contact.put("recentReceivedItem", this.recentReceivedItem);
        contact.put("displayText", this.name + " - " + this.phone);
        contact.put("contactType", "recipient");
        contact.put("lastUsedTime", this.lastReceiveTime);
        return contact;
    }

    /**
     * 合并到现有的联系人Map中（当同一人既是寄件人又是收件人时）
     */
    public void mergeIntoContactMap(Map<String, Object> existingContact) {
        existingContact.put("recipientCount", this.receiveCount);
        existingContact.put("totalUsageCount", (Integer)existingContact.get("senderCount") + this.receiveCount);
        existingContact.put("recentReceivedItem", this.recentReceivedItem);
        existingContact.put("contactType", "both");

        // 更新最近使用时间
        LocalDateTime lastSentTime = (LocalDateTime)existingContact.get("lastUsedTime");
        LocalDateTime lastReceivedTime = this.lastReceiveTime;
        if (lastSentTime != null && lastReceivedTime != null) {
            existingContact.put("lastUsedTime", lastSentTime.isAfter(lastReceivedTime) ? lastSentTime : lastReceivedTime);
        } else if (lastReceivedTime != null) {
            existingContact.put("lastUsedTime", lastReceivedTime);
        }
    }
}