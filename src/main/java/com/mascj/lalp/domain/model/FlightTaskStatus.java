package com.mascj.lalp.domain.model;

/**
 * 飞行任务状态枚举
 */
public enum FlightTaskStatus {
    /**
     * 已创建
     */
    CREATED("已创建"),
    
    /**
     * 执行中
     */
    EXECUTING("执行中"),
    
    /**
     * 已完成
     */
    COMPLETED("已完成"),
    
    /**
     * 已取消
     */
    CANCELLED("已取消"),
    
    /**
     * 失败
     */
    FAILED("失败"),
    
    /**
     * 暂停
     */
    PAUSED("暂停");

    private final String description;

    FlightTaskStatus(String description) {
        this.description = description;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据字符串获取枚举值
     */
    public static FlightTaskStatus fromString(String status) {
        if (status == null) {
            return null;
        }
        
        for (FlightTaskStatus taskStatus : FlightTaskStatus.values()) {
            if (taskStatus.name().equalsIgnoreCase(status)) {
                return taskStatus;
            }
        }
        
        throw new IllegalArgumentException("未知的飞行任务状态: " + status);
    }
}
