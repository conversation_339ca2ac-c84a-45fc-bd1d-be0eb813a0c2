package com.mascj.lalp.domain.model;

/**
 * 设备类型枚举
 */
public enum DeviceType {
    /**
     * 无人机设备
     */
    DRONE("DRONE", "无人机"),
    
    /**
     * 物流仓设备
     */
    WAREHOUSE("WAREHOUSE", "物流仓"),
    
    /**
     * 库存记录
     */
    INVENTORY("INVENTORY", "库存记录");

    private final String code;
    private final String description;

    DeviceType(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据代码获取设备类型
     * @param code 设备类型代码
     * @return 设备类型枚举
     */
    public static DeviceType fromCode(String code) {
        for (DeviceType type : values()) {
            if (type.code.equals(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Unknown device type code: " + code);
    }
}
