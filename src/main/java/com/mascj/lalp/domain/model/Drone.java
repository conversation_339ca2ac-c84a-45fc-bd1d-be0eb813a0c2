package com.mascj.lalp.domain.model;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import java.time.LocalDateTime;

@Data
@TableName("lalp_drone")
public class Drone {

    /**
     * 无人机的唯一标识符
     * 用于唯一标识一架无人机，是数据库中的主键
     */
    @TableId
    private Long id;

    /**
     * 无人机的名称
     * 用于记录无人机的名称，便于用户识别和管理
     */
    private String name;

    /**
     * 无人机的编号
     * 记录无人机的唯一编号，用于区分不同的无人机
     */
    @TableField("drone_id")
    private String droneId;

    /**
     * 任务计数
     * 记录该无人机已经执行的任务数量，用于评估无人机的使用频率
     */
    @TableField("mission_count")
    private Integer missionCount;

    /**
     * 无人机的当前位置
     * 记录无人机当前所在的位置，用于追踪和管理无人机的活动范围
     */
    private String location;

    /**
     * SIM卡号码
     * 记录无人机使用的SIM卡号码，用于通信和数据传输
     */
    @TableField("sim_card_number")
    private String simCardNumber;

    /**
     * 设备序列号
     * 记录无人机的设备序列号，用于设备的唯一标识和追踪
     */
    @TableField("device_sn")
    private String deviceSn;

    /**
     * 最后通信时间
     * 记录无人机最后一次与服务器通信的时间，用于监控无人机的在线状态
     */
    @TableField("last_communication_time")
    private LocalDateTime lastCommunicationTime;

    /**
     * 电池电量
     * 记录无人机当前的电池电量，用于评估无人机的剩余飞行时间
     */
    @TableField("battery_level")
    private Double batteryLevel;

    /**
     * 电池电压
     * 记录无人机电池的电压，用于监控电池健康状况
     */
    @TableField("battery_voltage")
    private Double batteryVoltage;

    /**
     * 租户标识
     * 记录无人机所属的租户标识，用于多租户环境下的数据隔离
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 无人机状态
     * 记录无人机当前的状态，如飞行中、待命等，用于实时监控无人机的活动状态
     * // 存储 ONLINE 或 OFFLINE
     */
    @TableField("status")
    private String status;
}