package com.mascj.lalp.domain.model;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;

/**
 * 飞行任务实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("lalp_flight_task")
public class FlightTask {
    
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 飞控系统返回的飞行任务ID
     */
    private String flightTaskId;
    
    /**
     * 关联的配送任务ID
     */
    private Long deliveryTaskId;
    
    /**
     * 设备SN（无人机编号）
     */
    private String deviceSn;
    
    /**
     * 任务名称
     */
    private String taskName;
    
    /**
     * 飞行高度（米）
     */
    private Double altitude;
    
    /**
     * 起飞点物流仓SN
     */
    private String startStoreSn;
    
    /**
     * 目标点物流仓SN
     */
    private String targetStoreSn;
    
    /**
     * 全局飞行速度（m/s）
     */
    private String speed;
    
    /**
     * 航线完成时动作（0=返航；1=悬停；2=降落）
     */
    private String finishAction;
    
    /**
     * 飞行任务状态
     */
    private FlightTaskStatus status;
    
    /**
     * 远程飞控系统的完整响应
     */
    private String remoteResponse;
    
    /**
     * 错误信息（失败时）
     */
    private String errorMessage;
    
    /**
     * 错误代码（失败时）
     */
    private String errorCode;
    
    /**
     * 创建人
     */
    private String createdBy;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 任务开始时间
     */
    private LocalDateTime startTime;
    
    /**
     * 任务结束时间
     */
    private LocalDateTime endTime;
    
    /**
     * 租户ID
     */
    private Long tenantId;
    
    /**
     * 创建成功的飞行任务
     */
    public static FlightTask createSuccess(String flightTaskId, Long deliveryTaskId, String deviceSn, 
                                         String taskName, Double altitude, String startStoreSn, 
                                         String targetStoreSn, String speed, String finishAction,
                                         String remoteResponse, String createdBy, Long tenantId) {
        return FlightTask.builder()
                .flightTaskId(flightTaskId)
                .deliveryTaskId(deliveryTaskId)
                .deviceSn(deviceSn)
                .taskName(taskName)
                .altitude(altitude)
                .startStoreSn(startStoreSn)
                .targetStoreSn(targetStoreSn)
                .speed(speed)
                .finishAction(finishAction)
                .status(FlightTaskStatus.CREATED)
                .remoteResponse(remoteResponse)
                .createdBy(createdBy)
                .tenantId(tenantId)
                .createTime(LocalDateTime.now())
                .updateTime(LocalDateTime.now())
                .build();
    }
    
    /**
     * 创建失败的飞行任务
     */
    public static FlightTask createFailure(Long deliveryTaskId, String deviceSn, String taskName, 
                                         Double altitude, String startStoreSn, String targetStoreSn,
                                         String speed, String finishAction, String errorMessage, 
                                         String errorCode, String createdBy, Long tenantId) {
        return FlightTask.builder()
                .deliveryTaskId(deliveryTaskId)
                .deviceSn(deviceSn)
                .taskName(taskName)
                .altitude(altitude)
                .startStoreSn(startStoreSn)
                .targetStoreSn(targetStoreSn)
                .speed(speed)
                .finishAction(finishAction)
                .status(FlightTaskStatus.FAILED)
                .errorMessage(errorMessage)
                .errorCode(errorCode)
                .createdBy(createdBy)
                .tenantId(tenantId)
                .createTime(LocalDateTime.now())
                .updateTime(LocalDateTime.now())
                .build();
    }
    
    /**
     * 标记任务为执行中
     */
    public void markAsExecuting() {
        this.status = FlightTaskStatus.EXECUTING;
        this.startTime = LocalDateTime.now();
        this.updateTime = LocalDateTime.now();
    }
    
    /**
     * 标记任务为已完成
     */
    public void markAsCompleted() {
        this.status = FlightTaskStatus.COMPLETED;
        this.endTime = LocalDateTime.now();
        this.updateTime = LocalDateTime.now();
    }
    
    /**
     * 标记任务为已取消
     */
    public void markAsCancelled() {
        this.status = FlightTaskStatus.CANCELLED;
        this.endTime = LocalDateTime.now();
        this.updateTime = LocalDateTime.now();
    }
    
    /**
     * 标记任务为失败
     */
    public void markAsFailed(String errorMessage, String errorCode) {
        this.status = FlightTaskStatus.FAILED;
        this.errorMessage = errorMessage;
        this.errorCode = errorCode;
        this.endTime = LocalDateTime.now();
        this.updateTime = LocalDateTime.now();
    }
}
