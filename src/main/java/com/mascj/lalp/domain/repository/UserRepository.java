package com.mascj.lalp.domain.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mascj.lalp.domain.model.User;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 用户数据访问接口
 */
@Mapper
public interface UserRepository extends BaseMapper<User> {
    
    /**
     * 根据手机号查询用户
     */
    default User findByPhone(String phone) {
        return selectOne(new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<User>()
                .eq(User::getPhone, phone));
    }
    
    /**
     * 根据openid查询用户
     */
    default User findByOpenid(String openid) {
        return selectOne(new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<User>()
                .eq(User::getOpenid, openid));
    }
    
    /**
     * 根据手机号和openid查询用户
     */
    default User findByPhoneAndOpenid(String phone, String openid) {
        return selectOne(new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<User>()
                .eq(User::getPhone, phone)
                .eq(User::getOpenid, openid));
    }
    /**
     * 根据外部用户ID查询用户
     */
    @Select("SELECT * FROM lalp_user WHERE outer_user_id = #{id}")
    User findByOuterUserId(@Param("id") Long id);

}
