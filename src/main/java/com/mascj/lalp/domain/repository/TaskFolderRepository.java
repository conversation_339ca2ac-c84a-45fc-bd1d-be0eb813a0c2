package com.mascj.lalp.domain.repository;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mascj.lalp.domain.model.TaskFolder;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 任务收藏夹Repository
 */
@Mapper
public interface TaskFolderRepository extends BaseMapper<TaskFolder> {

    /**
     * 查询所有收藏夹列表（包含任务数量和子收藏夹数量）
     * @param tenantId 租户ID
     * @return 收藏夹列表
     */
    @Select("SELECT f.*, " +
            "COALESCE(fav_count.task_count, 0) as task_count, " +
            "COALESCE(child_count.child_count, 0) as child_count, " +
            "p.folder_name as parent_name " +
            "FROM lalp_task_folder f " +
            "LEFT JOIN (" +
            "    SELECT folder_id, COUNT(*) as task_count " +
            "    FROM lalp_task_favorite " +
            "    WHERE deleted = 0 " +
            "    GROUP BY folder_id" +
            ") fav_count ON f.id = fav_count.folder_id " +
            "LEFT JOIN (" +
            "    SELECT parent_id, COUNT(*) as child_count " +
            "    FROM lalp_task_folder " +
            "    WHERE deleted = 0 AND parent_id IS NOT NULL " +
            "    GROUP BY parent_id" +
            ") child_count ON f.id = child_count.parent_id " +
            "LEFT JOIN lalp_task_folder p ON f.parent_id = p.id " +
            "WHERE f.tenant_id = #{tenantId} " +
            "AND f.deleted = 0 " +
            "ORDER BY f.folder_level ASC, f.sort_order ASC, f.create_time DESC")
    List<TaskFolder> selectAllFoldersWithCounts(@Param("tenantId") Long tenantId);

    /**
     * 获取指定父级下一个排序号
     * @param parentId 父级收藏夹ID
     * @param tenantId 租户ID
     * @return 下一个排序号
     */
    @Select("SELECT COALESCE(MAX(sort_order), 0) + 1 " +
            "FROM lalp_task_folder " +
            "WHERE (parent_id = #{parentId} OR (parent_id IS NULL AND #{parentId} IS NULL)) " +
            "AND tenant_id = #{tenantId} " +
            "AND deleted = 0")
    Integer getNextSortOrder(@Param("parentId") Long parentId,
                           @Param("tenantId") Long tenantId);

    /**
     * 检查收藏夹名称在同一父级下是否已存在
     * @param folderName 收藏夹名称
     * @param parentId 父级收藏夹ID
     * @param tenantId 租户ID
     * @param excludeId 排除的ID（用于更新时检查）
     * @return 存在的数量
     */
    @Select("<script>" +
            "SELECT COUNT(*) " +
            "FROM lalp_task_folder " +
            "WHERE folder_name = #{folderName} " +
            "AND (parent_id = #{parentId} OR (parent_id IS NULL AND #{parentId} IS NULL)) " +
            "AND tenant_id = #{tenantId} " +
            "AND deleted = 0 " +
            "<if test='excludeId != null'>" +
            "AND id != #{excludeId} " +
            "</if>" +
            "</script>")
    Integer countByFolderNameInParent(@Param("folderName") String folderName,
                                    @Param("parentId") Long parentId,
                                    @Param("tenantId") Long tenantId,
                                    @Param("excludeId") Long excludeId);

    /**
     * 查询可作为父级的收藏夹列表（排除自己和自己的子级）
     * @param tenantId 租户ID
     * @param excludeId 排除的收藏夹ID
     * @return 可选父级收藏夹列表
     */
    @Select("<script>" +
            "SELECT id, folder_name, folder_level, folder_path " +
            "FROM lalp_task_folder " +
            "WHERE tenant_id = #{tenantId} " +
            "AND deleted = 0 " +
            "<if test='excludeId != null'>" +
            "AND id != #{excludeId} " +
            "AND (folder_path IS NULL OR folder_path NOT LIKE CONCAT('%/', #{excludeId}, '/%')) " +
            "</if>" +
            "ORDER BY folder_level ASC, sort_order ASC" +
            "</script>")
    List<TaskFolder> selectAvailableParents(@Param("tenantId") Long tenantId,
                                          @Param("excludeId") Long excludeId);

    /**
     * 查询指定父级下的子收藏夹列表
     * @param parentId 父级收藏夹ID
     * @param tenantId 租户ID
     * @return 子收藏夹列表
     */
    @Select("SELECT f.*, " +
            "COALESCE(fav_count.task_count, 0) as task_count, " +
            "COALESCE(child_count.child_count, 0) as child_count " +
            "FROM lalp_task_folder f " +
            "LEFT JOIN (" +
            "    SELECT folder_id, COUNT(*) as task_count " +
            "    FROM lalp_task_favorite " +
            "    WHERE deleted = 0 " +
            "    GROUP BY folder_id" +
            ") fav_count ON f.id = fav_count.folder_id " +
            "LEFT JOIN (" +
            "    SELECT parent_id, COUNT(*) as child_count " +
            "    FROM lalp_task_folder " +
            "    WHERE deleted = 0 AND parent_id IS NOT NULL " +
            "    GROUP BY parent_id" +
            ") child_count ON f.id = child_count.parent_id " +
            "WHERE f.parent_id = #{parentId} " +
            "AND f.tenant_id = #{tenantId} " +
            "AND f.deleted = 0 " +
            "ORDER BY f.sort_order ASC, f.create_time DESC")
    List<TaskFolder> selectChildFolders(@Param("parentId") Long parentId,
                                       @Param("tenantId") Long tenantId);

    /**
     * 跨租户查询收藏夹（忽略租户限制）
     * 用于支持跨租户收藏功能
     * 使用原生SQL绕过多租户插件拦截
     *
     * @param id 收藏夹ID
     * @return 收藏夹信息
     */
    @Select(value = "SELECT id, folder_name, folder_desc, folder_icon, folder_color, " +
            "parent_id, folder_path, folder_level, sort_order, is_default, " +
            "creator_id, creator_name, tenant_id, create_time, update_time, deleted " +
            "FROM lalp_task_folder " +
            "WHERE id = #{id} AND deleted = 0")
    @InterceptorIgnore(tenantLine = "true")  // 忽略多租户插件
    TaskFolder selectByIdIgnoreTenant(@Param("id") Long id);
}
