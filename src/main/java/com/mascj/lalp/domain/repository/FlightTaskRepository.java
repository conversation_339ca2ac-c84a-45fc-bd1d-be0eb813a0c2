package com.mascj.lalp.domain.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mascj.lalp.domain.model.FlightTask;
import com.mascj.lalp.domain.model.FlightTaskStatus;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 飞行任务数据访问接口
 */
@Mapper
public interface FlightTaskRepository extends BaseMapper<FlightTask> {
    
    /**
     * 保存飞行任务
     */
    default FlightTask save(FlightTask flightTask) {
        if (flightTask.getId() == null) {
            insert(flightTask);
        } else {
            updateById(flightTask);
        }
        return flightTask;
    }
    
    /**
     * 根据ID查找飞行任务
     */
    default Optional<FlightTask> findById(Long id) {
        return Optional.ofNullable(selectById(id));
    }
    
    /**
     * 根据飞控系统任务ID查找飞行任务
     */
    default Optional<FlightTask> findByFlightTaskId(String flightTaskId) {
        LambdaQueryWrapper<FlightTask> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FlightTask::getFlightTaskId, flightTaskId);
        return Optional.ofNullable(selectOne(queryWrapper));
    }
    
    /**
     * 根据配送任务ID查找飞行任务
     */
    default List<FlightTask> findByDeliveryTaskId(Long deliveryTaskId) {
        LambdaQueryWrapper<FlightTask> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FlightTask::getDeliveryTaskId, deliveryTaskId)
                   .orderByDesc(FlightTask::getCreateTime);
        return selectList(queryWrapper);
    }
    
    /**
     * 根据设备SN查找飞行任务
     */
    default List<FlightTask> findByDeviceSn(String deviceSn) {
        LambdaQueryWrapper<FlightTask> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FlightTask::getDeviceSn, deviceSn)
                   .orderByDesc(FlightTask::getCreateTime);
        return selectList(queryWrapper);
    }
    
    /**
     * 根据状态查找飞行任务
     */
    default List<FlightTask> findByStatus(FlightTaskStatus status) {
        LambdaQueryWrapper<FlightTask> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FlightTask::getStatus, status)
                   .orderByDesc(FlightTask::getCreateTime);
        return selectList(queryWrapper);
    }
    
    /**
     * 分页查询飞行任务
     */
    default Page<FlightTask> findPage(Page<FlightTask> page, String deviceSn, FlightTaskStatus status, 
                                    LocalDateTime startTime, LocalDateTime endTime) {
        LambdaQueryWrapper<FlightTask> queryWrapper = new LambdaQueryWrapper<>();
        
        if (deviceSn != null && !deviceSn.trim().isEmpty()) {
            queryWrapper.eq(FlightTask::getDeviceSn, deviceSn);
        }
        
        if (status != null) {
            queryWrapper.eq(FlightTask::getStatus, status);
        }
        
        if (startTime != null) {
            queryWrapper.ge(FlightTask::getCreateTime, startTime);
        }
        
        if (endTime != null) {
            queryWrapper.le(FlightTask::getCreateTime, endTime);
        }
        
        queryWrapper.orderByDesc(FlightTask::getCreateTime);
        
        return selectPage(page, queryWrapper);
    }
    
    /**
     * 查询所有飞行任务
     */
    default List<FlightTask> findAll() {
        LambdaQueryWrapper<FlightTask> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.orderByDesc(FlightTask::getCreateTime);
        return selectList(queryWrapper);
    }
    
    /**
     * 统计飞行任务数量
     */
    default long countByStatus(FlightTaskStatus status) {
        LambdaQueryWrapper<FlightTask> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FlightTask::getStatus, status);
        return selectCount(queryWrapper);
    }
    
    /**
     * 统计设备的飞行任务数量
     */
    default long countByDeviceSn(String deviceSn) {
        LambdaQueryWrapper<FlightTask> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FlightTask::getDeviceSn, deviceSn);
        return selectCount(queryWrapper);
    }
    
    /**
     * 查找正在执行的飞行任务
     */
    default List<FlightTask> findExecutingTasks() {
        LambdaQueryWrapper<FlightTask> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FlightTask::getStatus, FlightTaskStatus.EXECUTING)
                   .orderByAsc(FlightTask::getStartTime);
        return selectList(queryWrapper);
    }
    
    /**
     * 查找指定时间范围内的飞行任务
     */
    default List<FlightTask> findByTimeRange(LocalDateTime startTime, LocalDateTime endTime) {
        LambdaQueryWrapper<FlightTask> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.between(FlightTask::getCreateTime, startTime, endTime)
                   .orderByDesc(FlightTask::getCreateTime);
        return selectList(queryWrapper);
    }
}
