package com.mascj.lalp.config;

import com.alibaba.cloud.nacos.NacosConfigManager;
import com.alibaba.cloud.nacos.NacosConfigProperties;
import com.alibaba.cloud.nacos.annotation.NacosAnnotationProcessor;
import org.springframework.beans.factory.BeanFactoryUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;

// @Configuration
public class NacosConfig {

    @Bean
    public NacosConfigProperties nacosConfigProperties(ApplicationContext context) {
        if (context.getParent() != null && BeanFactoryUtils.beanNamesForTypeIncludingAncestors(context.getParent(),
                NacosConfigProperties.class).length > 0) {
            return BeanFactoryUtils.beanOfTypeIncludingAncestors(context.getParent(), NacosConfigProperties.class);
        }
        if (NacosConfigManager.getInstance() == null) {
            return new NacosConfigProperties();
        } else {
            return NacosConfigManager.getInstance().getNacosConfigProperties();
        }
    }

    @Bean
    public static NacosAnnotationProcessor nacosAnnotationProcessor() {
        return new NacosAnnotationProcessor();
    }
}
