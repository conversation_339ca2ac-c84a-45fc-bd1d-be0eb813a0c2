package com.mascj.lalp.config;

/**
 * 多租户跳过控制器
 */
public class TenantSkipController {
    private static final ThreadLocal<Boolean> SKIP_TENANT_FILTER = new ThreadLocal<>();
    
    public static void skipTenantFilter() {
        SKIP_TENANT_FILTER.set(true);
    }
    
    public static void enableTenantFilter() {
        SKIP_TENANT_FILTER.remove();
    }
    
    public static boolean shouldSkipTenantFilter() {
        return Boolean.TRUE.equals(SKIP_TENANT_FILTER.get());
    }
}
