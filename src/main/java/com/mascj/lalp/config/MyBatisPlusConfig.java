package com.mascj.lalp.config;

import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.handler.TenantLineHandler;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.TenantLineInnerInterceptor;
import com.mascj.lalp.common.tenant.TenantIdResolver;
import net.sf.jsqlparser.expression.Expression;
import net.sf.jsqlparser.expression.LongValue;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * MyBatis-Plus 配置类
 */
@Configuration
public class MyBatisPlusConfig {
    
    private final TenantIdResolver tenantIdResolver;
    
    public MyBatisPlusConfig(TenantIdResolver tenantIdResolver) {
        this.tenantIdResolver = tenantIdResolver;
    }
    
    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
        
        // 添加多租户拦截器
        interceptor.addInnerInterceptor(new TenantLineInnerInterceptor(new TenantLineHandler() {
            @Override
            public Expression getTenantId() {
                // 如果设置了跳过多租户过滤，返回 null
                if (TenantSkipController.shouldSkipTenantFilter()) {
                    return null;
                }

                Long tenantId = tenantIdResolver.resolveTenantId();
                return tenantId != null ? new LongValue(tenantId) : null;
            }

            @Override
            public String getTenantIdColumn() {
                return "tenant_id";
            }

            // 这是 default 方法,默认返回 false 表示所有表都需要拼多租户条件
            @Override
            public boolean ignoreTable(String tableName) {
                // 如果设置了跳过多租户过滤，忽略所有表
                if (TenantSkipController.shouldSkipTenantFilter()) {
                    return true;
                }

                // 配置不需要多租户的表
                return List.of(
                    "lalp_warehouse_location",
                    "lalp_inventory",
                    "lalp_user",
                    "lalp_inventory_detail",
                    "lalp_recipient",
                    "lalp_sender"
                ).contains(tableName);
            }
        }));
        
        // 添加分页插件
        interceptor.addInnerInterceptor(new PaginationInnerInterceptor(DbType.MYSQL));
        
        return interceptor;
    }
}
