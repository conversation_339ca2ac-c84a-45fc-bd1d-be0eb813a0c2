package com.mascj.lalp.config;

import com.mascj.lalp.common.interceptor.TenantInterceptor;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * WebMVC配置
 */
@Configuration
@RequiredArgsConstructor
public class WebMvcConfig implements WebMvcConfigurer {

    @Autowired
    private TenantInterceptor tenantInterceptor;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(tenantInterceptor)
                .addPathPatterns("/api/**")  // 拦截所有API请求
                .excludePathPatterns(
                        "/api/health",
                        "/api/ping",
                        "/api/users/login",
                        "/api/users/wechat-user-info",
                        "/api/users/wechat-login",
                        "/api/users/wechat-phone",
                        "/api/drone/*/approaching"
                );  // 明确排除不需要token验证的路径
    }
}
