package com.mascj.lalp.infrastructure.scheduled;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mascj.lalp.application.service.TenantService;
import com.mascj.lalp.common.util.TenantUtils;
import com.mascj.lalp.common.context.TenantContext;
import com.mascj.lalp.config.TenantConfig;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.dao.DuplicateKeyException;
import com.mascj.lalp.domain.model.Warehouse;
import com.mascj.lalp.domain.model.WarehouseStatus;
import com.mascj.lalp.domain.model.Drone;
import com.mascj.lalp.domain.repository.WarehouseRepository;
import com.mascj.lalp.domain.repository.DroneRepository;
import com.mascj.lalp.interfaces.feign.DataCenterFeign;
import com.mascj.lalp.interfaces.feign.dto.BaseResponse;
import com.mascj.lalp.interfaces.feign.dto.DeviceDTO;
import com.mascj.lalp.interfaces.feign.dto.DeviceListRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.List;

import static com.mascj.lalp.domain.model.WarehouseType.LOGISTICS;

/**
 * 定时同步设备列表任务
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class DeviceSyncTask {
    private final DataCenterFeign dataCenterFeign;
    private final WarehouseRepository warehouseRepository;
    private final DroneRepository droneRepository;
    private final TenantService tenantService;
    private final TenantUtils tenantUtils;
    private final TenantConfig tenantConfig;
    private final ObjectMapper objectMapper;
    /**
     * 10分钟调用一次远程接口获取设备列表
     * 为每个活跃租户执行设备同步任务，确保多租户隔离
     *
     * initialDelay: 应用启动后延迟30秒再执行，避免启动时数据库未完全就绪
     * fixedRate: 每10分钟执行一次
     */
    @Scheduled(initialDelay = 30000, fixedRate = 600000) // 启动延迟30秒，然后每10分钟执行一次
    public void syncDeviceList() {
        log.info("开始执行定时任务：同步设备列表");

        // 获取活跃租户ID列表
        List<Long> activeTenantIds = getActiveTenantIds();

        if (CollectionUtils.isEmpty(activeTenantIds)) {
            log.warn("未找到活跃租户，跳过设备同步任务");
            return;
        }

        log.info("找到 {} 个活跃租户，开始为每个租户同步设备", activeTenantIds.size());

        // 为每个租户执行设备同步
        for (Long tenantId : activeTenantIds) {
            syncDeviceListForTenant(tenantId);
        }

        log.info("所有租户设备同步任务完成");
    }

    /**
     * 获取活跃租户ID列表
     * 优先从数据库动态获取，失败时使用配置的默认租户ID
     */
    private List<Long> getActiveTenantIds() {
        try {
            // 优先从数据库动态获取活跃租户ID
            if (tenantConfig.isEnableDynamicDiscovery()) {
                List<Long> tenantIds = tenantService.getActiveTenantIds();
                if (!CollectionUtils.isEmpty(tenantIds)) {
                    log.debug("从数据库获取到活跃租户ID: {}", tenantIds);
                    return tenantIds;
                }
                log.warn("动态获取租户ID失败，将使用配置的默认租户ID");
            }

            // 使用配置的默认租户ID作为备用方案
            List<Long> defaultTenantIds = tenantConfig.getDefaultTenantIds();
            if (!CollectionUtils.isEmpty(defaultTenantIds)) {
                log.info("使用配置的默认租户ID: {}", defaultTenantIds);
                return defaultTenantIds;
            }

            log.error("无法获取任何租户ID，请检查配置");
            return List.of();

        } catch (Exception e) {
            log.error("获取活跃租户ID失败", e);

            // 异常情况下尝试使用配置的默认租户ID
            List<Long> defaultTenantIds = tenantConfig.getDefaultTenantIds();
            if (!CollectionUtils.isEmpty(defaultTenantIds)) {
                log.warn("异常情况下使用配置的默认租户ID: {}", defaultTenantIds);
                return defaultTenantIds;
            }

            return List.of();
        }
    }

    /**
     * 为指定租户同步设备列表
     */
    private void syncDeviceListForTenant(Long tenantId) {
        try {
            log.info("开始为租户 {} 同步设备列表", tenantId);

            // 在租户上下文中执行同步操作
            tenantUtils.executeInTenantContext(tenantId, () -> {
                try {
                    // 同步物流仓设备（类型300）
                    syncWarehouseDevices(tenantId);

                    // 同步无人机设备（类型0 - 飞机类）
                    syncDroneDevices(tenantId);

                } catch (Exception e) {
                    log.error("租户 {} 设备同步异常", tenantId, e);
                }
            });

        } catch (Exception e) {
            log.error("租户 {} 设备同步任务执行失败", tenantId, e);
        }
    }

    /**
     * 同步物流仓设备（类型300）
     */
    private void syncWarehouseDevices(Long tenantId) {
        try {
            // 构建请求参数
            DeviceListRequest request = new DeviceListRequest();
            request.setTenantId(String.valueOf(tenantId));
            request.setType(300); // 物流仓设备类型

            // 调用 Feign 客户端
            BaseResponse<List<DeviceDTO>> response = dataCenterFeign.getDeviceList(request);

            if (Boolean.TRUE.equals(response.getSuccess()) && response.getData() != null) {
                List<DeviceDTO> devices = response.getData();
                for (DeviceDTO device : devices) {
                    Warehouse warehouse = convertToWarehouse(device);
                    saveOrUpdateWarehouse(warehouse);
                }
                log.info("租户 {} 成功同步物流仓设备数量: {}", tenantId, devices.size());
            } else {
                log.warn("租户 {} 物流仓设备同步失败，状态码：{}，消息：{}",
                        tenantId, response.getCode(), response.getMsg());
            }
        } catch (Exception e) {
            log.error("租户 {} 物流仓设备同步异常", tenantId, e);
        }
    }

    /**
     * 同步无人机设备（类型0 - 飞机类）
     */
    private void syncDroneDevices(Long tenantId) {
        try {
            // 构建请求参数
            DeviceListRequest request = new DeviceListRequest();
            request.setTenantId(String.valueOf(tenantId));
            request.setType(0); // 飞机类设备类型

            // 调用 Feign 客户端
            BaseResponse<List<DeviceDTO>> response = dataCenterFeign.getDeviceList(request);

            if (Boolean.TRUE.equals(response.getSuccess()) && response.getData() != null) {
                List<DeviceDTO> devices = response.getData();
                for (DeviceDTO device : devices) {
                    Drone drone = convertToDrone(device);
                    saveOrUpdateDrone(drone);
                }
                log.info("租户 {} 成功同步无人机设备数量: {}", tenantId, devices.size());
            } else {
                log.warn("租户 {} 无人机设备同步失败，状态码：{}，消息：{}",
                        tenantId, response.getCode(), response.getMsg());
            }
        } catch (Exception e) {
            log.error("租户 {} 无人机设备同步异常", tenantId, e);
        }
    }
    private Warehouse convertToWarehouse(DeviceDTO device) {
        Warehouse warehouse = new Warehouse();
        warehouse.setName(device.getName()); // 名称
        warehouse.setCode(device.getSn()); // SN 作为 code
        warehouse.setAddress(device.getLocation()); // 位置作为地址
        warehouse.setStatus(mapOnlineStatus(device.getState())); // 在线状态转换
        warehouse.setType(LOGISTICS);// 类型设为物流仓
        warehouse.setTenantId((Long.valueOf(device.getTenantId()))); // 租户ID
        warehouse.setOuterWarehouseId(Long.valueOf(device.getModelId()));
        // 解析 location 中的经纬度
        String locationJson = device.getLocation();
        if (locationJson != null && !locationJson.isEmpty()) {
            try {
                JsonNode rootNode = objectMapper.readTree(locationJson);
                JsonNode coordinatesNode = rootNode
                        .path("geometry")
                        .path("coordinates");

                if (coordinatesNode.isArray() && coordinatesNode.size() >= 2) {
                    warehouse.setLongitude(String.valueOf(coordinatesNode.get(0).asDouble()));
                    warehouse.setLatitude(String.valueOf(coordinatesNode.get(1).asDouble()));
                } else {
                    warehouse.setLongitude(null);
                    warehouse.setLatitude(null);
                }
            } catch (Exception e) {
                log.warn("解析设备位置信息失败: {}", locationJson, e);
            }
        } else {
            warehouse.setLongitude(null);
            warehouse.setLatitude(null);
        }
        return warehouse;
    }

    private WarehouseStatus mapOnlineStatus(Integer online) {
        return online != null && online == 1 ? WarehouseStatus.ONLINE : WarehouseStatus.OFFLINE;
    }

    private void saveOrUpdateWarehouse(Warehouse warehouse) {
        Warehouse existing = warehouseRepository.findByCode(warehouse.getCode());
        if (existing != null) {
            warehouse.setId(existing.getId());
            warehouse.setCreateTime(existing.getCreateTime());
            warehouseRepository.updateById(warehouse);
            log.info("已更新仓库信息: {}", warehouse.getName());
        } else {
            try {
                warehouse.setCreateTime(LocalDateTime.now());
                warehouseRepository.insert(warehouse);
                log.info("已新增仓库: {}", warehouse.getName());
            } catch (DuplicateKeyException e) {
                // 并发下兜底：如果插入时发现已存在，则查出来再更新
                log.warn("插入仓库时发现唯一索引冲突，尝试更新: {}", warehouse.getCode());
                Warehouse dbWarehouse = warehouseRepository.findByCode(warehouse.getCode());
                if (dbWarehouse != null) {
                    warehouse.setId(dbWarehouse.getId());
                    warehouse.setCreateTime(dbWarehouse.getCreateTime());
                    warehouseRepository.updateById(warehouse);
                    log.info("并发下已更新仓库信息: {}", warehouse.getName());
                }
            }
        }
    }

    /**
     * 将设备信息转换为无人机信息
     * 根据远程设备数据映射到数据库字段
     */
    private Drone convertToDrone(DeviceDTO device) {
        Drone drone = new Drone();

        // 基础信息映射
        drone.setName(device.getName());                    // 设备名称 -> name
        drone.setDroneId(device.getSn());                   // 设备序列号 -> drone_id (无人机编号)
        drone.setDeviceSn(device.getSn());                  // 设备序列号 -> device_sn

        // 位置信息处理
        String location = parseLocationInfo(device.getLocation());
        drone.setLocation(location);                        // 位置信息 -> location

        // 状态映射：远程状态 -> 数据库状态
        drone.setStatus(mapDeviceStatusToString(device.getState())); // 状态 -> status

        // 默认值设置
        drone.setMissionCount(0);                           // 默认作业架次为0
        drone.setTenantId(TenantContext.getTenantId());     // 当前租户ID

        // 通信相关信息（根据DeviceDTO实际字段映射）
        // 流量卡信息：使用 networkCard 字段
        if (device.getNetworkCard() != null && !device.getNetworkCard().equals("--")) {
            drone.setSimCardNumber(device.getNetworkCard());    // networkCard -> sim_card_number
        }

        // 注意：DeviceDTO中没有电池相关字段，这些信息可能需要从其他接口获取
        // 或者在后续版本中添加到DeviceDTO中

        // 最后通信时间：使用 updateTime 作为最后通信时间
        if (device.getUpdateTime() != null) {
            // 将 Date 转换为 LocalDateTime
            drone.setLastCommunicationTime(
                device.getUpdateTime().toInstant()
                    .atZone(java.time.ZoneId.systemDefault())
                    .toLocalDateTime()
            );
        }

        return drone;
    }

    /**
     * 保存或更新无人机信息
     * 智能更新：只更新从远程同步的字段，保留本地维护的字段
     */
    private void saveOrUpdateDrone(Drone drone) {
        try {
            // 根据设备SN查找现有无人机（跨租户查询）
            Drone existingDrone = droneRepository.selectByDeviceSnIgnoreTenant(drone.getDeviceSn());

            if (existingDrone != null) {
                // 更新从远程同步的字段
                existingDrone.setName(drone.getName());                    // 更新设备名称
                existingDrone.setLocation(drone.getLocation());            // 更新位置信息
                existingDrone.setStatus(drone.getStatus());                // 更新在线状态

                // 更新通信相关信息（如果有新数据）
                if (drone.getSimCardNumber() != null) {
                    existingDrone.setSimCardNumber(drone.getSimCardNumber());
                }
                if (drone.getLastCommunicationTime() != null) {
                    existingDrone.setLastCommunicationTime(drone.getLastCommunicationTime());
                }

                // 注意：电池信息暂时不从DeviceDTO同步，因为该DTO中没有相关字段
                // 这些信息可能需要从其他专门的设备状态接口获取

                // 保持本地维护的字段不变：
                // - missionCount (作业架次)
                // - droneId (无人机编号，如果本地有自定义)
                // - 其他业务相关字段

                droneRepository.updateById(existingDrone);
                log.info("已更新无人机信息: {} (SN: {})", existingDrone.getName(), existingDrone.getDeviceSn());
            } else {
                // 创建新的无人机记录
                droneRepository.insert(drone);
                log.info("已创建新无人机: {} (SN: {})", drone.getName(), drone.getDeviceSn());
            }
        } catch (DuplicateKeyException e) {
            // 处理并发插入的情况
            log.warn("无人机插入时发现唯一索引冲突，尝试更新: {}", drone.getDeviceSn());
            Drone dbDrone = droneRepository.selectByDeviceSnIgnoreTenant(drone.getDeviceSn());
            if (dbDrone != null) {
                // 递归调用更新逻辑
                saveOrUpdateDrone(drone);
            }
        } catch (Exception e) {
            log.error("保存无人机信息失败: {} (SN: {})", drone.getName(), drone.getDeviceSn(), e);
        }
    }

    /**
     * 解析位置信息
     * 处理GeoJSON格式位置数据，提取经纬度并转换为可读地址
     */
    private String parseLocationInfo(String locationData) {
        if (locationData == null || locationData.trim().isEmpty() || "--".equals(locationData)) {
            return null;
        }

        // 如果是JSON格式，尝试解析GeoJSON
        if (locationData.startsWith("{")) {
            try {
                JsonNode rootNode = objectMapper.readTree(locationData);

                // 解析GeoJSON格式: {"geometry":{"type":"Point","coordinates":[118.55489448399966,31.697551132158242]},"properties":""}
                if (rootNode.has("geometry")) {
                    JsonNode geometry = rootNode.get("geometry");
                    if (geometry.has("type") && "Point".equals(geometry.get("type").asText())
                        && geometry.has("coordinates")) {

                        JsonNode coordinates = geometry.get("coordinates");
                        if (coordinates.isArray() && coordinates.size() >= 2) {
                            double longitude = coordinates.get(0).asDouble(); // 经度
                            double latitude = coordinates.get(1).asDouble();  // 纬度

                            // 格式化经纬度信息
                            String coordinateStr = String.format("%.6f,%.6f", longitude, latitude);

                            // 尝试获取地址信息（如果有的话）
                            String addressInfo = getAddressFromCoordinates(longitude, latitude);

                            if (addressInfo != null && !addressInfo.trim().isEmpty()) {
                                String result = addressInfo + " (" + coordinateStr + ")";
                                return result.length() > 90 ? result.substring(0, 87) + "..." : result;
                            } else {
                                return "经纬度: " + coordinateStr;
                            }
                        }
                    }
                }

                // 尝试提取其他格式的经纬度信息
                if (rootNode.has("latitude") && rootNode.has("longitude")) {
                    double lat = rootNode.get("latitude").asDouble();
                    double lng = rootNode.get("longitude").asDouble();
                    return "经纬度: " + String.format("%.6f,%.6f", lng, lat);
                }

                // 解析失败时截断原始数据
                return locationData.length() > 90 ? locationData.substring(0, 90) + "..." : locationData;

            } catch (Exception e) {
                log.warn("解析位置JSON失败: {}", locationData, e);
                // 解析失败时截断原始数据
                return locationData.length() > 90 ? locationData.substring(0, 90) + "..." : locationData;
            }
        }

        // 对于非JSON格式的位置信息，直接截断
        return locationData.length() > 90 ? locationData.substring(0, 90) + "..." : locationData;
    }

    /**
     * 根据经纬度获取地址信息
     * 这里可以集成第三方地理编码服务，如高德地图、百度地图等
     */
    private String getAddressFromCoordinates(double longitude, double latitude) {
        try {
            // 简单的地理位置判断（可以根据需要扩展）
            String region = getRegionByCoordinates(longitude, latitude);
            if (region != null) {
                return region;
            }

            // TODO: 这里可以集成第三方地理编码API
            // 例如：高德地图逆地理编码API
            // String address = callGeocodingAPI(longitude, latitude);
            // return address;

            return null; // 暂时返回null，使用经纬度显示

        } catch (Exception e) {
            log.warn("获取地址信息失败: lng={}, lat={}", longitude, latitude, e);
            return null;
        }
    }

    /**
     * 根据经纬度简单判断地理区域
     * 这是一个简化的实现，实际项目中建议使用专业的地理编码服务
     */
    private String getRegionByCoordinates(double longitude, double latitude) {
        // 中国大陆范围大致判断
        if (longitude >= 73.0 && longitude <= 135.0 && latitude >= 18.0 && latitude <= 54.0) {

            // 一些主要城市的大致范围判断（示例）
            if (longitude >= 116.0 && longitude <= 117.0 && latitude >= 39.5 && latitude <= 40.5) {
                return "北京市";
            } else if (longitude >= 121.0 && longitude <= 122.0 && latitude >= 31.0 && latitude <= 32.0) {
                return "上海市";
            } else if (longitude >= 113.0 && longitude <= 115.0 && latitude >= 22.0 && latitude <= 24.0) {
                return "广东省";
            } else if (longitude >= 118.0 && longitude <= 120.0 && latitude >= 31.0 && latitude <= 33.0) {
                return "江苏省";
            } else if (longitude >= 119.0 && longitude <= 122.0 && latitude >= 29.0 && latitude <= 31.0) {
                return "浙江省";
            }

            return "中国"; // 在中国范围内但无法确定具体省市
        }

        return null; // 不在已知范围内
    }

    /**
     * 映射远程设备状态到数据库状态字符串
     * 根据远程设备的状态值映射到数据库中的状态字符串
     */
    private String mapDeviceStatusToString(Integer deviceState) {
        if (deviceState == null) {
            return "IDLE";
        }

        switch (deviceState) {
            case 1:
                return "ONLINE";   // 在线
            case 0:
                return "OFFLINE";  // 离线
            default:
                return "IDLE";     // 空闲/未知状态
        }
    }

    /**
     * 映射设备状态到无人机状态（保留原方法以兼容）
     * @deprecated 使用 mapDeviceStatusToString 替代
     */
    @Deprecated
    private String mapDroneStatus(Integer deviceState) {
        return mapDeviceStatusToString(deviceState);
    }
}