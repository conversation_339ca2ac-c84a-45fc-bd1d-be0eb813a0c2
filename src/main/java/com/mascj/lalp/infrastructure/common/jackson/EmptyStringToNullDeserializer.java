package com.mascj.lalp.infrastructure.common.jackson;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonToken;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonNode;
import com.mascj.lalp.interfaces.rest.backend.dto.RemoteFlightTaskResponse;

import java.io.IOException;

/**
 * 自定义反序列化器，将空字符串转换为null
 * 用于处理远程接口返回空字符串而不是null的情况
 */
public class EmptyStringToNullDeserializer extends JsonDeserializer<RemoteFlightTaskResponse.FlightTaskData> {

    @Override
    public RemoteFlightTaskResponse.FlightTaskData deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
        JsonToken token = p.getCurrentToken();
        
        // 如果是字符串类型
        if (token == JsonToken.VALUE_STRING) {
            String value = p.getValueAsString();
            // 如果是空字符串或null，返回null
            if (value == null || value.trim().isEmpty()) {
                return null;
            }
            // 如果是非空字符串，这可能是个错误，也返回null
            return null;
        }
        
        // 如果是null值，直接返回null
        if (token == JsonToken.VALUE_NULL) {
            return null;
        }
        
        // 如果是对象，正常反序列化
        if (token == JsonToken.START_OBJECT) {
            JsonNode node = p.getCodec().readTree(p);
            RemoteFlightTaskResponse.FlightTaskData data = new RemoteFlightTaskResponse.FlightTaskData();
            
            if (node.has("id") && !node.get("id").isNull()) {
                data.setId(node.get("id").asText());
            }
            
            if (node.has("jobId") && !node.get("jobId").isNull()) {
                data.setJobId(node.get("jobId").asText());
            }
            
            return data;
        }
        
        // 其他情况返回null
        return null;
    }
}
