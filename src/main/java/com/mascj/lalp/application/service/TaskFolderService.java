package com.mascj.lalp.application.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mascj.lalp.common.context.TenantContext;
import com.mascj.lalp.config.TenantSkipController;
import com.mascj.lalp.domain.model.DeliveryTask;
import com.mascj.lalp.domain.model.TaskFavorite;
import com.mascj.lalp.domain.model.TaskFolder;
import com.mascj.lalp.domain.repository.TaskFavoriteRepository;
import com.mascj.lalp.domain.repository.TaskFolderRepository;
import com.mascj.lalp.infrastructure.common.api.PageResult;
import com.mascj.lalp.interfaces.rest.backend.dto.TaskFavoriteRequest;
import com.mascj.lalp.interfaces.rest.backend.dto.TaskFolderRequest;
import com.mascj.lalp.interfaces.rest.backend.dto.TaskFolderResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 任务收藏夹服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TaskFolderService {

    private final TaskFolderRepository taskFolderRepository;
    private final TaskFavoriteRepository taskFavoriteRepository;
    private final UserService userService;

    /**
     * 创建收藏夹
     * @param request 创建请求
     * @param creatorId 创建人ID
     * @param creatorName 创建人姓名
     * @param tenantId 租户ID
     * @return 创建的收藏夹
     */
    @Transactional(rollbackFor = Exception.class)
    public TaskFolder createFolder(TaskFolderRequest request, Long creatorId, String creatorName, Long tenantId) {
        log.info("创建收藏夹: folderName={}, creatorId={}, tenantId={}", 
                request.getFolderName(), creatorId, tenantId);

        // 1. 检查父级收藏夹是否存在
        TaskFolder parentFolder = null;
        if (request.getParentId() != null) {
            parentFolder = taskFolderRepository.selectById(request.getParentId());
            if (parentFolder == null || parentFolder.getDeleted()) {
                throw new IllegalArgumentException("父级收藏夹不存在");
            }
        }

        // 2. 检查收藏夹名称在同一父级下是否已存在
        Integer existCount = taskFolderRepository.countByFolderNameInParent(
                request.getFolderName(), request.getParentId(), tenantId, null);
        if (existCount > 0) {
            throw new IllegalArgumentException("同一父级下收藏夹名称已存在");
        }

        // 3. 获取排序号
        Integer sortOrder = request.getSortOrder();
        if (sortOrder == null) {
            sortOrder = taskFolderRepository.getNextSortOrder(request.getParentId(), tenantId);
        }

        // 4. 创建收藏夹
        TaskFolder folder = TaskFolder.fromRequest(request, creatorId, creatorName, tenantId);
        // 设置排序号（可能来自请求或自动生成）
        folder.setSortOrder(sortOrder);

        taskFolderRepository.insert(folder);

        // 5. 构建路径和层级信息
        folder.buildPathAndLevel(parentFolder);
        taskFolderRepository.updateById(folder);

        log.info("收藏夹创建成功: id={}, folderName={}, parentId={}, level={}",
                folder.getId(), folder.getFolderName(), folder.getParentId(), folder.getFolderLevel());
        return folder;
    }

    /**
     * 更新收藏夹
     * @param folderId 收藏夹ID
     * @param request 更新请求
     * @param creatorId 创建人ID
     * @param tenantId 租户ID
     * @return 更新后的收藏夹
     */
    @Transactional(rollbackFor = Exception.class)
    public TaskFolder updateFolder(Long folderId, TaskFolderRequest request, Long creatorId, Long tenantId) {
        log.info("更新收藏夹: folderId={}, folderName={}, creatorId={}", 
                folderId, request.getFolderName(), creatorId);

        // 1. 查询收藏夹
        TaskFolder folder = taskFolderRepository.selectById(folderId);
        if (folder == null || folder.getDeleted()) {
            throw new IllegalArgumentException("收藏夹不存在");
        }

        // 2. 检查权限
        if (!folder.getCreatorId().equals(creatorId) || !folder.getTenantId().equals(tenantId)) {
            throw new IllegalArgumentException("无权限操作此收藏夹");
        }

        // 3. 检查收藏夹名称在同一父级下是否已存在（排除当前收藏夹）
        Integer existCount = taskFolderRepository.countByFolderNameInParent(
                request.getFolderName(), request.getParentId(), tenantId, folderId);
        if (existCount > 0) {
            throw new IllegalArgumentException("同一父级下收藏夹名称已存在");
        }

        // 4. 更新收藏夹
        folder.updateFromRequest(request);

        taskFolderRepository.updateById(folder);

        log.info("收藏夹更新成功: id={}, folderName={}", folder.getId(), folder.getFolderName());
        return folder;
    }

    /**
     * 删除收藏夹
     * @param folderId 收藏夹ID
     * @param creatorId 创建人ID
     * @param tenantId 租户ID
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteFolder(Long folderId, Long creatorId, Long tenantId) {
        log.info("删除收藏夹: folderId={}, creatorId={}", folderId, creatorId);

        // 1. 查询收藏夹
        TaskFolder folder = taskFolderRepository.selectById(folderId);
        if (folder == null || folder.getDeleted()) {
            throw new IllegalArgumentException("收藏夹不存在");
        }

        // 2. 检查权限
        if (!folder.getCreatorId().equals(creatorId) || !folder.getTenantId().equals(tenantId)) {
            throw new IllegalArgumentException("无权限操作此收藏夹");
        }

        // 3. 检查是否为默认收藏夹
        if (folder.getIsDefault()) {
            throw new IllegalArgumentException("默认收藏夹不能删除");
        }

        // 4. 删除收藏夹中的所有任务收藏记录
        taskFavoriteRepository.deleteByFolder(folderId, creatorId, tenantId);

        // 5. 删除收藏夹
        taskFolderRepository.deleteById(folderId);

        log.info("收藏夹删除成功: id={}", folderId);
    }

    /**
     * 查询所有收藏夹列表（不区分用户）
     * @param tenantId 租户ID
     * @return 收藏夹列表
     */
    public List<TaskFolder> listAllFolders(Long tenantId) {
        log.info("查询所有收藏夹列表: tenantId={}", tenantId);

        List<TaskFolder> folders = taskFolderRepository.selectAllFoldersWithCounts(tenantId);

        log.info("查询收藏夹列表完成: tenantId={}, count={}", tenantId, folders.size());
        return folders;
    }

    /**
     * 查询用户的收藏夹列表
     * @param creatorId 创建人ID
     * @param tenantId 租户ID
     * @return 收藏夹列表
     */
    public List<TaskFolder> listUserFolders(Long creatorId, Long tenantId) {
        log.info("查询用户收藏夹列表: creatorId={}, tenantId={}", creatorId, tenantId);

        LambdaQueryWrapper<TaskFolder> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TaskFolder::getCreatorId, creatorId)
                   .eq(TaskFolder::getTenantId, tenantId)
                   .eq(TaskFolder::getDeleted, false)
                   .orderBy(true, true, TaskFolder::getSortOrder)
                   .orderBy(true, true, TaskFolder::getCreateTime);

        List<TaskFolder> folders = taskFolderRepository.selectList(queryWrapper);

        log.info("查询用户收藏夹列表完成: creatorId={}, count={}", creatorId, folders.size());
        return folders;
    }

    /**
     * 查询可作为父级的收藏夹列表
     * @param tenantId 租户ID
     * @param excludeId 排除的收藏夹ID（避免循环引用）
     * @return 可选父级收藏夹列表
     */
    public List<TaskFolder> listAvailableParents(Long tenantId, Long excludeId) {
        log.info("查询可选父级收藏夹: tenantId={}, excludeId={}", tenantId, excludeId);

        List<TaskFolder> folders = taskFolderRepository.selectAvailableParents(tenantId, excludeId);

        log.info("查询可选父级收藏夹完成: tenantId={}, count={}", tenantId, folders.size());
        return folders;
    }

    /**
     * 查询指定父级下的子收藏夹列表
     * @param parentId 父级收藏夹ID
     * @param tenantId 租户ID
     * @return 子收藏夹列表
     */
    public List<TaskFolder> listChildFolders(Long parentId, Long tenantId) {
        log.info("查询子收藏夹列表: parentId={}, tenantId={}", parentId, tenantId);

        List<TaskFolder> folders = taskFolderRepository.selectChildFolders(parentId, tenantId);

        log.info("查询子收藏夹列表完成: parentId={}, count={}", parentId, folders.size());
        return folders;
    }

    /**
     * 获取收藏夹详情
     * @param folderId 收藏夹ID
     * @param creatorId 创建人ID
     * @param tenantId 租户ID
     * @return 收藏夹详情
     */
    public TaskFolder getFolderDetail(Long folderId, Long creatorId, Long tenantId) {
        log.info("获取收藏夹详情: folderId={}, creatorId={}", folderId, creatorId);

        TaskFolder folder = taskFolderRepository.selectById(folderId);
        if (folder == null || folder.getDeleted()) {
            return null;
        }

        // 检查权限
        if (!folder.getCreatorId().equals(creatorId) || !folder.getTenantId().equals(tenantId)) {
            return null;
        }

        return folder;
    }

    /**
     * 获取收藏夹详情（跨租户，不检查权限）
     * @param folderId 收藏夹ID
     * @return 收藏夹详情
     */
    public TaskFolder getFolderDetailCrossTenant(Long folderId) {
        log.info("获取收藏夹详情（跨租户）: folderId={}", folderId);

        try {
            // 临时跳过多租户过滤
            TenantSkipController.skipTenantFilter();

            TaskFolder folder = taskFolderRepository.selectById(folderId);
            if (folder == null || folder.getDeleted()) {
                return null;
            }

            return folder;
        } finally {
            // 恢复多租户过滤
            TenantSkipController.enableTenantFilter();
        }
    }

    /**
     * 收藏任务到收藏夹
     * @param request 收藏请求
     * @param creatorId 创建人ID
     * @param creatorName 创建人姓名
     * @param tenantId 租户ID
     * @return 收藏记录
     */
    @Transactional(rollbackFor = Exception.class)
    public TaskFavorite addTaskToFolder(TaskFavoriteRequest request, Long creatorId, String creatorName, Long tenantId) {
        log.info("收藏任务: folderId={}, taskId={}, creatorId={}", 
                request.getFolderId(), request.getTaskId(), creatorId);

        // 1. 检查收藏夹是否存在
        TaskFolder folder = getFolderDetail(request.getFolderId(), creatorId, tenantId);
        if (folder == null) {
            throw new IllegalArgumentException("收藏夹不存在或无权限访问");
        }

        // 2. 检查任务是否已收藏
        Integer existCount = taskFavoriteRepository.countByFolderAndTask(
                request.getFolderId(), request.getTaskId(), creatorId, tenantId);
        if (existCount > 0) {
            throw new IllegalArgumentException("任务已收藏到此收藏夹");
        }

        // 3. 创建收藏记录
        TaskFavorite favorite = TaskFavorite.fromRequest(request, creatorId, creatorName, tenantId);

        taskFavoriteRepository.insert(favorite);

        log.info("任务收藏成功: id={}, folderId={}, taskId={}",
                favorite.getId(), request.getFolderId(), request.getTaskId());
        return favorite;
    }

    /**
     * 收藏任务到收藏夹（跨租户版本）
     * @param request 收藏请求
     * @param creatorId 创建人ID
     * @param creatorName 创建人姓名
     * @param tenantId 租户ID
     * @return 收藏记录
     */
    @Transactional(rollbackFor = Exception.class)
    public TaskFavorite addTaskToFolderCrossTenant(TaskFavoriteRequest request, Long creatorId, String creatorName, Long tenantId) {
        log.info("收藏任务（跨租户）: folderId={}, taskId={}, creatorId={}",
                request.getFolderId(), request.getTaskId(), creatorId);

        // 1. 检查收藏夹是否存在（跨租户）
        TaskFolder folder = getFolderDetailCrossTenant(request.getFolderId());
        if (folder == null) {
            throw new IllegalArgumentException("收藏夹不存在");
        }

        // 2. 检查任务是否已收藏（在当前用户的租户下）
        Integer existCount = taskFavoriteRepository.countByFolderAndTask(
                request.getFolderId(), request.getTaskId(), creatorId, tenantId);
        if (existCount > 0) {
            throw new IllegalArgumentException("任务已收藏到此收藏夹");
        }

        // 3. 创建收藏记录（使用当前用户的租户ID）
        TaskFavorite favorite = TaskFavorite.fromRequest(request, creatorId, creatorName, tenantId);

        taskFavoriteRepository.insert(favorite);

        log.info("任务收藏成功（跨租户）: id={}, folderId={}, taskId={}, userTenantId={}, folderTenantId={}",
                favorite.getId(), request.getFolderId(), request.getTaskId(), tenantId, folder.getTenantId());
        return favorite;
    }

    /**
     * 从收藏夹移除任务
     * @param folderId 收藏夹ID
     * @param taskId 任务ID
     * @param creatorId 创建人ID
     * @param tenantId 租户ID
     */
    @Transactional(rollbackFor = Exception.class)
    public void removeTaskFromFolder(Long folderId, Long taskId, Long creatorId, Long tenantId) {
        log.info("移除任务收藏: folderId={}, taskId={}, creatorId={}", folderId, taskId, creatorId);

        LambdaQueryWrapper<TaskFavorite> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TaskFavorite::getFolderId, folderId)
                   .eq(TaskFavorite::getTaskId, taskId)
                   .eq(TaskFavorite::getCreatorId, creatorId)
                   .eq(TaskFavorite::getTenantId, tenantId)
                   .eq(TaskFavorite::getDeleted, false);

        TaskFavorite favorite = taskFavoriteRepository.selectOne(queryWrapper);
        if (favorite != null) {
            taskFavoriteRepository.deleteById(favorite.getId());
            log.info("任务收藏移除成功: id={}", favorite.getId());
        }
    }

    /**
     * 分页查询收藏夹中的任务
     * @param folderId 收藏夹ID（可选，为null时查询所有被收藏的任务）
     * @param pageNum 页码
     * @param pageSize 页大小
     * @param creatorId 创建人ID
     * @param tenantId 租户ID
     * @return 任务分页结果
     */
    public PageResult<DeliveryTask> listFolderTasks(Long folderId, int pageNum, int pageSize,
                                                   Long creatorId, Long tenantId) {
        log.info("查询收藏夹任务: folderId={}, pageNum={}, pageSize={}, creatorId={}",
                folderId, pageNum, pageSize, creatorId);

        Page<DeliveryTask> page = new Page<>(pageNum, pageSize);
        Page<DeliveryTask> taskPage;

        if (folderId != null) {
            // 查询指定收藏夹中的任务
            // 1. 检查收藏夹权限
            TaskFolder folder = getFolderDetail(folderId, creatorId, tenantId);
            if (folder == null) {
                throw new IllegalArgumentException("收藏夹不存在或无权限访问");
            }

            // 2. 分页查询指定收藏夹的任务
            taskPage = taskFavoriteRepository.selectTasksByFolder(page, folderId, creatorId, tenantId);
            log.info("查询指定收藏夹任务完成: folderId={}, total={}, current={}",
                    folderId, taskPage.getTotal(), taskPage.getCurrent());
        } else {
            // 查询所有被收藏的任务
            taskPage = taskFavoriteRepository.selectAllFavoriteTasks(page, creatorId, tenantId);
            log.info("查询所有被收藏任务完成: total={}, current={}",
                    taskPage.getTotal(), taskPage.getCurrent());
        }

        return PageResult.of(
                taskPage.getCurrent(),
                taskPage.getSize(),
                taskPage.getTotal(),
                taskPage.getRecords()
        );
    }

    /**
     * 获取或创建默认收藏夹
     * @param creatorId 创建人ID
     * @param creatorName 创建人姓名
     * @param tenantId 租户ID
     * @return 默认收藏夹
     */
    @Transactional(rollbackFor = Exception.class)
    public TaskFolder getOrCreateDefaultFolder(Long creatorId, String creatorName, Long tenantId) {
        log.info("获取或创建默认收藏夹: creatorId={}, tenantId={}", creatorId, tenantId);

        // 查找现有的默认收藏夹
        LambdaQueryWrapper<TaskFolder> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TaskFolder::getIsDefault, true)
                   .eq(TaskFolder::getTenantId, tenantId)
                   .eq(TaskFolder::getDeleted, false);

        TaskFolder defaultFolder = taskFolderRepository.selectOne(queryWrapper);

        if (defaultFolder == null) {
            // 创建默认收藏夹
            TaskFolderRequest request = TaskFolderRequest.builder()
                .folderName("我的收藏")
                .folderDesc("默认收藏夹")
                .folderIcon("star")
                .folderColor("#faad14")
                .isDefault(true)
                .sortOrder(0)
                .build();

            defaultFolder = createFolder(request, creatorId, creatorName, tenantId);
            log.info("创建默认收藏夹成功: id={}", defaultFolder.getId());
        }

        return defaultFolder;
    }

    /**
     * 检查任务是否已收藏到指定收藏夹
     * @param taskId 任务ID
     * @param folderId 收藏夹ID
     * @param creatorId 创建人ID
     * @param tenantId 租户ID
     * @return 是否已收藏
     */
    public boolean isTaskFavorited(Long taskId, Long folderId, Long creatorId, Long tenantId) {
        Integer count = taskFavoriteRepository.countByFolderAndTask(folderId, taskId, creatorId, tenantId);
        return count > 0;
    }

    /**
     * 获取任务的所有收藏记录
     * @param taskId 任务ID
     * @param creatorId 创建人ID
     * @param tenantId 租户ID
     * @return 收藏记录列表
     */
    public List<TaskFavorite> getTaskFavorites(Long taskId, Long creatorId, Long tenantId) {
        return taskFavoriteRepository.selectByTask(taskId, creatorId, tenantId);
    }

    /**
     * 批量查询任务收藏状态
     * @param taskIds 任务ID列表
     * @param creatorId 创建人ID
     * @param tenantId 租户ID
     * @return 任务ID -> 是否收藏的映射
     */
    public Map<Long, Boolean> getBatchTaskFavoriteStatus(List<Long> taskIds, Long creatorId, Long tenantId) {
        if (taskIds == null || taskIds.isEmpty()) {
            return new HashMap<>();
        }

        // 查询这些任务的收藏记录
        LambdaQueryWrapper<TaskFavorite> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(TaskFavorite::getTaskId, taskIds)
                   .eq(TaskFavorite::getCreatorId, creatorId)
                   .eq(TaskFavorite::getTenantId, tenantId)
                   .eq(TaskFavorite::getDeleted, false);

        List<TaskFavorite> favorites = taskFavoriteRepository.selectList(queryWrapper);
        Set<Long> favoritedTaskIds = favorites.stream()
            .map(TaskFavorite::getTaskId)
            .collect(Collectors.toSet());

        // 构建结果映射
        Map<Long, Boolean> result = new HashMap<>();
        for (Long taskId : taskIds) {
            result.put(taskId, favoritedTaskIds.contains(taskId));
        }

        return result;
    }

    // ==================== 带认证的业务方法 ====================

    /**
     * 创建收藏夹（带认证）
     * @param token 认证token
     * @param request 创建请求
     * @return 收藏夹响应
     */
    public TaskFolderResponse createFolderWithAuth(String token, TaskFolderRequest request) {
        try {
            // 认证和获取用户信息
            AuthContext authContext = validateTokenAndGetContext(token);

            // 创建收藏夹
            TaskFolder folder = createFolder(request, authContext.getUserId(), authContext.getUserName(), authContext.getTenantId());

            log.info("收藏夹创建成功: id={}, folderName={}", folder.getId(), folder.getFolderName());
            return TaskFolderResponse.of(folder);

        } catch (IllegalArgumentException e) {
            log.warn("创建收藏夹失败: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("创建收藏夹异常", e);
            throw new RuntimeException("创建收藏夹失败: " + e.getMessage(), e);
        }
    }

    /**
     * 查询收藏夹列表（带认证）
     * @param token 认证token
     * @return 收藏夹列表
     */
    public List<TaskFolderResponse> listFoldersWithAuth(String token) {
        try {
            // 认证和获取用户信息
            AuthContext authContext = validateTokenAndGetContext(token);

            // 查询所有收藏夹（不区分用户，但按租户隔离）
            List<TaskFolder> folders = listAllFolders(authContext.getTenantId());
            List<TaskFolderResponse> responses = folders.stream()
                    .map(TaskFolderResponse::of)
                    .collect(Collectors.toList());

            log.info("查询收藏夹列表成功: count={}", responses.size());
            return responses;

        } catch (Exception e) {
            log.error("查询收藏夹列表异常", e);
            throw new RuntimeException("查询收藏夹列表失败: " + e.getMessage(), e);
        }
    }

    /**
     * 收藏任务到收藏夹（带认证）
     * @param token 认证token
     * @param request 收藏请求
     */
    public void addTaskToFolderWithAuth(String token, TaskFavoriteRequest request) {
        try {
            // 认证和获取用户信息
            AuthContext authContext = validateTokenAndGetContext(token);

            // 收藏任务
            addTaskToFolder(request, authContext.getUserId(), authContext.getUserName(), authContext.getTenantId());

            log.info("任务收藏成功: folderId={}, taskId={}", request.getFolderId(), request.getTaskId());

        } catch (IllegalArgumentException e) {
            log.warn("收藏任务失败: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("收藏任务异常", e);
            throw new RuntimeException("收藏任务失败: " + e.getMessage(), e);
        }
    }

    /**
     * 快速收藏任务（带认证）
     * @param token 认证token
     * @param taskId 任务ID
     * @return 收藏结果
     */
    public Map<String, Object> quickFavoriteTaskWithAuth(String token, Long taskId) {
        try {
            // 认证和获取用户信息
            AuthContext authContext = validateTokenAndGetContext(token);

            // 查找或创建默认收藏夹
            TaskFolder defaultFolder = getOrCreateDefaultFolder(authContext.getUserId(), authContext.getUserName(), authContext.getTenantId());

            // 检查是否已收藏
            boolean isAlreadyFavorited = isTaskFavorited(taskId, defaultFolder.getId(), authContext.getUserId(), authContext.getTenantId());

            Map<String, Object> result = new HashMap<>();
            result.put("folderId", defaultFolder.getId());
            result.put("folderName", defaultFolder.getFolderName());

            if (isAlreadyFavorited) {
                // 如果已收藏，则取消收藏
                removeTaskFromFolder(defaultFolder.getId(), taskId, authContext.getUserId(), authContext.getTenantId());
                log.info("任务取消收藏成功: taskId={}", taskId);

                result.put("favorited", false);
                result.put("message", "取消收藏成功");
            } else {
                // 如果未收藏，则添加收藏
                TaskFavoriteRequest request = TaskFavoriteRequest.builder()
                    .folderId(defaultFolder.getId())
                    .taskId(taskId)
                    .favoriteNote("快速收藏")
                    .build();

                addTaskToFolder(request, authContext.getUserId(), authContext.getUserName(), authContext.getTenantId());
                log.info("任务快速收藏成功: taskId={}, folderId={}", taskId, defaultFolder.getId());

                result.put("favorited", true);
                result.put("message", "收藏成功");
            }

            return result;

        } catch (Exception e) {
            log.error("快速收藏任务异常", e);
            throw new RuntimeException("快速收藏失败: " + e.getMessage(), e);
        }
    }

    /**
     * 查询任务收藏状态（带认证）
     * @param token 认证token
     * @param taskId 任务ID
     * @return 收藏状态信息
     */
    public Map<String, Object> getTaskFavoriteStatusWithAuth(String token, Long taskId) {
        try {
            // 认证和获取用户信息
            AuthContext authContext = validateTokenAndGetContext(token);

            // 查询任务的所有收藏记录
            List<TaskFavorite> favorites = getTaskFavorites(taskId, authContext.getUserId(), authContext.getTenantId());

            boolean isFavorited = !favorites.isEmpty();
            List<Map<String, Object>> folderList = favorites.stream()
                .map(favorite -> {
                    Map<String, Object> map = new HashMap<>();
                    map.put("folderId", favorite.getFolderId());
                    map.put("favoriteNote", favorite.getFavoriteNote() != null ? favorite.getFavoriteNote() : "");
                    map.put("createTime", favorite.getCreateTime());
                    return map;
                })
                .collect(Collectors.toList());

            log.info("查询任务收藏状态成功: taskId={}, isFavorited={}, folderCount={}",
                taskId, isFavorited, folderList.size());

            Map<String, Object> result = new HashMap<>();
            result.put("taskId", taskId);
            result.put("favorited", isFavorited);
            result.put("favoriteCount", folderList.size());
            result.put("folders", folderList);

            return result;

        } catch (Exception e) {
            log.error("查询任务收藏状态异常", e);
            throw new RuntimeException("查询收藏状态失败: " + e.getMessage(), e);
        }
    }

    /**
     * 批量查询任务收藏状态（带认证）
     * @param token 认证token
     * @param taskIds 任务ID列表
     * @return 收藏状态映射
     */
    public Map<Long, Boolean> getBatchTaskFavoriteStatusWithAuth(String token, List<Long> taskIds) {
        try {
            // 认证和获取用户信息
            AuthContext authContext = validateTokenAndGetContext(token);

            // 批量查询收藏状态
            Map<Long, Boolean> favoriteStatusMap = getBatchTaskFavoriteStatus(taskIds, authContext.getUserId(), authContext.getTenantId());

            log.info("批量查询任务收藏状态成功: count={}", favoriteStatusMap.size());
            return favoriteStatusMap;

        } catch (Exception e) {
            log.error("批量查询任务收藏状态异常", e);
            throw new RuntimeException("批量查询收藏状态失败: " + e.getMessage(), e);
        }
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 验证token并获取认证上下文
     * @param token 认证token
     * @return 认证上下文
     */
    private AuthContext validateTokenAndGetContext(String token) {
        // 后端特有的token验证
        Long userId = userService.getCurrentUserId(token);
        if (userId == null) {
            throw new IllegalArgumentException("无效的认证信息");
        }

        // 获取租户ID
        Long tenantId = TenantContext.getTenantId();
        if (tenantId == null) {
            tenantId = 1L; // 默认租户ID
        }

        return new AuthContext(userId, "系统用户", tenantId);
    }

    /**
     * 认证上下文内部类
     */
    private static class AuthContext {
        private final Long userId;
        private final String userName;
        private final Long tenantId;

        public AuthContext(Long userId, String userName, Long tenantId) {
            this.userId = userId;
            this.userName = userName;
            this.tenantId = tenantId;
        }

        public Long getUserId() { return userId; }
        public String getUserName() { return userName; }
        public Long getTenantId() { return tenantId; }
    }

    /**
     * 智能切换收藏状态
     * 根据任务当前收藏状态，执行收藏或取消收藏操作
     *
     * @param taskId 任务ID
     * @param folderId 收藏夹ID（收藏时需要）
     * @param userId 用户ID
     * @param userName 用户名
     * @param tenantId 租户ID
     * @return 操作结果
     */
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> smartToggleFavorite(Long taskId, Long folderId,
                                                   Long userId, String userName, Long tenantId) {

        log.info("执行智能收藏切换: taskId={}, folderId={}, userId={}, tenantId={}",
                taskId, folderId, userId, tenantId);

        // 1. 业务参数验证
        if (taskId == null || taskId <= 0) {
            throw new IllegalArgumentException("任务ID不能为空且必须大于0");
        }
        if (userId == null || userId <= 0) {
            throw new IllegalArgumentException("用户ID不能为空且必须大于0");
        }
        if (tenantId == null || tenantId <= 0) {
            throw new IllegalArgumentException("租户ID不能为空且必须大于0");
        }

        // 2. 设置租户上下文
        TenantContext.setTenantId(tenantId);

        try {
            // 3. 检查任务当前收藏状态
            List<TaskFavorite> existingFavorites = getTaskFavorites(taskId, userId, tenantId);

            Map<String, Object> result = new HashMap<>();
            result.put("taskId", taskId);
            result.put("userId", userId);
            result.put("timestamp", System.currentTimeMillis());

            if (!existingFavorites.isEmpty()) {
                // 4. 任务已收藏，执行取消收藏操作
                return executeUnfavoriteOperation(taskId, userId, tenantId, existingFavorites, result);
            } else {
                // 5. 任务未收藏，执行收藏操作
                return executeFavoriteOperation(taskId, folderId, userId, userName, tenantId, result);
            }

        } finally {
            // 6. 清理租户上下文
            TenantContext.clear();
        }
    }

    /**
     * 执行取消收藏操作
     */
    private Map<String, Object> executeUnfavoriteOperation(Long taskId, Long userId, Long tenantId,
                                                          List<TaskFavorite> existingFavorites,
                                                          Map<String, Object> result) {

        log.info("执行取消收藏操作: taskId={}, userId={}, favoriteCount={}",
                taskId, userId, existingFavorites.size());

        // 从所有收藏夹中移除该任务
        int removedCount = 0;
        for (TaskFavorite favorite : existingFavorites) {
            try {
                removeTaskFromFolder(favorite.getFolderId(), taskId, userId, tenantId);
                removedCount++;
                log.debug("从收藏夹移除任务: folderId={}, taskId={}", favorite.getFolderId(), taskId);
            } catch (Exception e) {
                log.warn("从收藏夹移除任务失败: folderId={}, taskId={}, error={}",
                        favorite.getFolderId(), taskId, e.getMessage());
            }
        }

        // 构建响应结果
        result.put("action", "unfavorite");
        result.put("favorited", false);
        result.put("message", "取消收藏成功");
        result.put("removedCount", removedCount);
        result.put("originalFavoriteCount", existingFavorites.size());

        log.info("取消收藏操作完成: taskId={}, removedCount={}/{}",
                taskId, removedCount, existingFavorites.size());

        return result;
    }

    /**
     * 执行收藏操作
     */
    private Map<String, Object> executeFavoriteOperation(Long taskId, Long folderId,
                                                        Long userId, String userName, Long tenantId,
                                                        Map<String, Object> result) {

        log.info("执行收藏操作: taskId={}, folderId={}, userId={}", taskId, folderId, userId);

        // 验证收藏夹ID
        if (folderId == null || folderId <= 0) {
            throw new IllegalArgumentException("收藏任务时必须指定有效的收藏夹ID");
        }

        // 验证收藏夹是否存在（跨租户访问）
        TaskFolder targetFolder = taskFolderRepository.selectByIdIgnoreTenant(folderId);
        if (targetFolder == null) {
            throw new IllegalArgumentException("指定的收藏夹不存在: " + folderId);
        }

        // 执行收藏操作
        TaskFavoriteRequest favoriteRequest = TaskFavoriteRequest.builder()
                .folderId(folderId)
                .taskId(taskId)
                .favoriteNote("智能收藏")
                .build();

        addTaskToFolderCrossTenant(favoriteRequest, userId, userName, tenantId);

        // 构建响应结果
        result.put("action", "favorite");
        result.put("favorited", true);
        result.put("folderId", folderId);
        result.put("folderName", targetFolder.getFolderName());
        result.put("message", "收藏成功");

        log.info("收藏操作完成: taskId={}, folderId={}, folderName={}",
                taskId, folderId, targetFolder.getFolderName());

        return result;
    }

    /**
     * 快速收藏任务到默认收藏夹
     *
     * @param taskId 任务ID
     * @param userId 用户ID
     * @param userName 用户名
     * @param tenantId 租户ID
     * @return 操作结果
     */
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> quickFavoriteTask(Long taskId, Long userId, String userName, Long tenantId) {

        log.info("快速收藏任务: taskId={}, userId={}, tenantId={}", taskId, userId, tenantId);

        // 1. 参数验证
        if (taskId == null || taskId <= 0) {
            throw new IllegalArgumentException("任务ID不能为空且必须大于0");
        }
        if (userId == null || userId <= 0) {
            throw new IllegalArgumentException("用户ID不能为空且必须大于0");
        }

        // 2. 设置租户上下文
        TenantContext.setTenantId(tenantId);

        try {
            // 3. 查找或创建默认收藏夹
            TaskFolder defaultFolder = findOrCreateDefaultFolder(userId, tenantId);

            // 4. 检查是否已经收藏
            List<TaskFavorite> existingFavorites = getTaskFavorites(taskId, userId, tenantId);
            boolean alreadyFavorited = existingFavorites.stream()
                    .anyMatch(fav -> fav.getFolderId().equals(defaultFolder.getId()));

            if (alreadyFavorited) {
                throw new IllegalArgumentException("任务已在默认收藏夹中");
            }

            // 5. 执行收藏操作
            TaskFavoriteRequest favoriteRequest = TaskFavoriteRequest.builder()
                    .folderId(defaultFolder.getId())
                    .taskId(taskId)
                    .favoriteNote("快速收藏")
                    .build();

            addTaskToFolderCrossTenant(favoriteRequest, userId, userName, tenantId);

            // 6. 构建响应结果
            Map<String, Object> result = new HashMap<>();
            result.put("taskId", taskId);
            result.put("userId", userId);
            result.put("action", "favorite");
            result.put("favorited", true);
            result.put("folderId", defaultFolder.getId());
            result.put("folderName", defaultFolder.getFolderName());
            result.put("message", "快速收藏成功");
            result.put("timestamp", System.currentTimeMillis());

            log.info("快速收藏成功: taskId={}, folderId={}, folderName={}",
                    taskId, defaultFolder.getId(), defaultFolder.getFolderName());

            return result;

        } finally {
            // 7. 清理租户上下文
            TenantContext.clear();
        }
    }

    /**
     * 查找或创建默认收藏夹
     */
    private TaskFolder findOrCreateDefaultFolder(Long userId, Long tenantId) {
        // 查找用户的默认收藏夹
        LambdaQueryWrapper<TaskFolder> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TaskFolder::getCreatorId, userId)
                   .eq(TaskFolder::getFolderName, "默认收藏夹")
                   .eq(TaskFolder::getTenantId, tenantId)
                   .orderByAsc(TaskFolder::getCreateTime)
                   .last("LIMIT 1");

        TaskFolder defaultFolder = taskFolderRepository.selectOne(queryWrapper);

        if (defaultFolder == null) {
            // 创建默认收藏夹
            TaskFolderRequest folderRequest = new TaskFolderRequest();
            folderRequest.setFolderName("默认收藏夹");

            defaultFolder = createFolder(folderRequest, userId, "系统", tenantId);
            log.info("为用户创建默认收藏夹: userId={}, folderId={}", userId, defaultFolder.getId());
        }

        return defaultFolder;
    }

    /**
     * 收藏任务到指定收藏夹
     *
     * @param taskId 任务ID
     * @param folderId 收藏夹ID
     * @param userId 用户ID
     * @param userName 用户名
     * @param tenantId 租户ID
     * @return 操作结果
     */
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> addTaskToFolder(Long taskId, Long folderId,
                                              Long userId, String userName, Long tenantId) {

        log.info("收藏任务到收藏夹: taskId={}, folderId={}, userId={}, tenantId={}",
                taskId, folderId, userId, tenantId);

        // 1. 参数验证
        if (taskId == null || taskId <= 0) {
            throw new IllegalArgumentException("任务ID不能为空且必须大于0");
        }
        if (folderId == null || folderId <= 0) {
            throw new IllegalArgumentException("收藏夹ID不能为空且必须大于0");
        }
        if (userId == null || userId <= 0) {
            throw new IllegalArgumentException("用户ID不能为空且必须大于0");
        }

        // 2. 设置租户上下文
        TenantContext.setTenantId(tenantId);

        try {
            // 3. 检查收藏夹是否存在（跨租户访问，所有收藏夹都可以收藏）
            TaskFolder targetFolder = taskFolderRepository.selectByIdIgnoreTenant(folderId);
            if (targetFolder == null) {
                throw new IllegalArgumentException("指定的收藏夹不存在: " + folderId);
            }

            // 4. 检查是否已经收藏到该收藏夹
            List<TaskFavorite> existingFavorites = getTaskFavorites(taskId, userId, tenantId);
            boolean alreadyInFolder = existingFavorites.stream()
                    .anyMatch(fav -> fav.getFolderId().equals(folderId));

            if (alreadyInFolder) {
                throw new IllegalArgumentException("任务已在指定收藏夹中");
            }

            // 5. 执行收藏操作
            TaskFavoriteRequest favoriteRequest = TaskFavoriteRequest.builder()
                    .folderId(folderId)
                    .taskId(taskId)
                    .favoriteNote("收藏到指定收藏夹")
                    .build();

            addTaskToFolderCrossTenant(favoriteRequest, userId, userName, tenantId);

            // 6. 构建响应结果
            Map<String, Object> result = new HashMap<>();
            result.put("action", "favorite");
            result.put("favorited", true);
            result.put("message", "收藏成功");
            result.put("taskId", taskId);
            result.put("folderId", folderId);
            result.put("folderName", targetFolder.getFolderName());
            result.put("timestamp", System.currentTimeMillis());

            log.info("收藏任务成功: taskId={}, folderId={}, folderName={}",
                    taskId, folderId, targetFolder.getFolderName());

            return result;

        } finally {
            // 7. 清理租户上下文
            TenantContext.clear();
        }
    }

    /**
     * 跨租户查询收藏夹详情
     *
     * @param folderId 收藏夹ID
     * @return 收藏夹信息
     */
    public TaskFolder getFolderByIdIgnoreTenant(Long folderId) {
        if (folderId == null || folderId <= 0) {
            throw new IllegalArgumentException("收藏夹ID不能为空且必须大于0");
        }

        return taskFolderRepository.selectByIdIgnoreTenant(folderId);
    }
}
