package com.mascj.lalp.application.service.sms;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 消息模板VO
 */
@Data
@Schema(description = "消息模板")
public class MsgTemplateVO {

    @Schema(description = "短信参数")
    private Map<String, Object> params;

    @Schema(description = "租户ID")
    private Long tenantId;

    @Schema(description = "短信模板类型")
    private String type;

    @Schema(description = "来源类型：1-机场，2-配送系统")
    private Integer sourceType;

    @Schema(description = "用户ID列表")
    private List<Long> userIds;
}
