package com.mascj.lalp.application.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mascj.lalp.domain.model.Warehouse;
import com.mascj.lalp.domain.model.WarehouseLocation;
import com.mascj.lalp.domain.model.WarehouseType;
import com.mascj.lalp.domain.repository.WarehouseRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@Transactional
@RequiredArgsConstructor
public class WarehouseService {
    private final WarehouseRepository warehouseRepository;

    /**
     * 分页查询物流仓列表
     *
     * @param page 分页参数
     * @param keyword 搜索关键词（可空）
     * @return 分页结果
     */
    public Page<Warehouse> page(Page<Warehouse> page, String keyword) {
        return warehouseRepository.page(page, keyword);
    }

    /**
     * 创建物流仓
     */
    public Warehouse createWarehouse(String name, WarehouseType type, String warehouseId, Long outerWarehouseId) {
        Warehouse warehouse = new Warehouse(name, type, warehouseId, outerWarehouseId);
        warehouseRepository.insert(warehouse);
        return warehouse;
    }

    /**
     * 根据ID获取物流仓
     */
    public Warehouse getWarehouseById(Long id) {
        return warehouseRepository.selectById(id);
    }

    /**
     * 根据编码获取物流仓
     */
    public Warehouse getWarehouseByCode(String code) {
        return warehouseRepository.selectByCode(code);
    }

    /**
     * 获取所有物流仓
     */
    public List<Warehouse> getAllWarehouses() {
        return warehouseRepository.selectList(null);
    }

    /**
     * 根据租户ID获取物流仓列表
     * @param tenantId 租户ID，如果为null则查询所有
     * @return 物流仓列表
     */
    public List<Warehouse> getWarehousesByTenantId(Long tenantId) {
        if (tenantId != null) {
            LambdaQueryWrapper<Warehouse> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(Warehouse::getTenantId, tenantId);
            return warehouseRepository.selectList(queryWrapper);
        } else {
            return warehouseRepository.selectList(null);
        }
    }

    public List<WarehouseLocation> listWarehouseLocations(Long warehouseId) {
        return warehouseRepository.selectWarehouseLocations(warehouseId);
    }
}