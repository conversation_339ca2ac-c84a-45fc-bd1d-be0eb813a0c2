package com.mascj.lalp.application.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mascj.lalp.common.util.PageUtils;
import com.mascj.lalp.common.util.QueryUtils;
import com.mascj.lalp.domain.exception.ResourceNotFoundException;
import com.mascj.lalp.domain.model.*;
import com.mascj.lalp.domain.repository.DeliveryTaskRepository;
import com.mascj.lalp.domain.repository.OrderRepository;
import com.mascj.lalp.interfaces.rest.backend.dto.OrderPageQuery;
import com.mascj.lalp.interfaces.rest.dto.CreateOrderRequest;

import com.mascj.lalp.interfaces.rest.dto.OpenBoxForPickedUpRequest;
import com.mascj.lalp.interfaces.rest.dto.OpenBoxForSendingRequest;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import com.mascj.lalp.domain.repository.SenderRepository;
import com.mascj.lalp.domain.repository.RecipientRepository;
import com.mascj.lalp.domain.repository.UserRepository;
import com.mascj.lalp.domain.repository.WarehouseRepository;
import com.mascj.lalp.application.service.sms.SmsService;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

import com.mascj.lalp.domain.repository.WarehouseRepository;

@Service
@Slf4j
@RequiredArgsConstructor
public class OrderService {
    private final OrderRepository orderRepository;
    private final SenderRepository senderRepository;
    private final RecipientRepository recipientRepository;
    private final UserRepository userRepository;
    private final SmsService smsService;
    private final DeliveryTaskRepository deliveryTaskRepository;
    private final WarehouseRepository warehouseRepository;

    @Transactional
    public Order createOrder(Order order) {
        // 确保订单有租户ID
        if (order.getTenantId() == null) {
            order.setTenantId(com.mascj.lalp.common.context.TenantContext.getTenantId());
        }
        
        // 查询发货仓信息
        var fromWarehouse = warehouseRepository.selectByCode(order.getFromWarehouseCode());
        if (fromWarehouse == null) {
            throw new ResourceNotFoundException("发货仓不存在: " + order.getFromWarehouseCode());
        }

        // 查询收货仓信息
        var toWarehouse = warehouseRepository.selectByCode(order.getToWarehouseCode());
        if (toWarehouse == null) {
            throw new ResourceNotFoundException("收货仓不存在: " + order.getToWarehouseCode());
        }

        // 创建订单并设置仓库信息
        order.setFromWarehouseName(fromWarehouse.getName());
        order.setToWarehouseName(toWarehouse.getName());
        // 生成订单号
        String orderNo = generateOrderNumber();
        order.setOrderNo(orderNo);

        // 生成寄件码和取件码（如果还没有的话）
        if (order.getSendCode() == null) {
            String sendCode = String.format("%06d", (int)(Math.random() * 1000000));
            order.setSendCode(sendCode);
        }
        if (order.getPickupCode() == null) {
            String pickupCode = String.format("%06d", (int)(Math.random() * 1000000));
            order.setPickupCode(pickupCode);
        }
        // 处理寄件人信息
        senderRepository.saveIfNotExists(order.getSenderName(), order.getSenderPhone(), order.getCargoContent(),
                order.getTenantId());
        recipientRepository.saveIfNotExists(order.getReceiverName(), order.getReceiverPhone(), order.getCargoContent(),
                order.getTenantId());

        // 保存订单
        Order savedOrder = orderRepository.save(order);

        // 发送预约寄件短信通知
        sendOrderPackageSms(savedOrder);

        return savedOrder;
    }

    public List<Order> getOrderList() {
        return orderRepository.findAll();
    }

    /**
     * 查询指定用户最近一周的订单
     * 
     * @param userId 用户ID
     * @return 最近一周的订单列表
     */
    public List<Order> findOrdersFromLastWeek(Long userId) {
        if (userId == null) {
            return orderRepository.findByCreateTimeAfter(LocalDateTime.now().minusWeeks(1));
        }
        return orderRepository.findByUserIdAndCreateTimeAfter(userId, LocalDateTime.now().minusWeeks(1));
    }

    /**
     * 根据订单号查询订单
     * 
     * @param orderNo 订单号
     * @return 订单信息
     */
    public Order findByOrderNo(String orderNo) {
        return orderRepository.findByOrderNo(orderNo);
    }

    /**
     * 根据ID查询订单
     * 
     * @param id 订单ID
     * @return 订单信息
     */
    public Order findById(Long id) {
        return orderRepository.findById(id);
    }

    /**
     * 创建预约订单
     * 
     * @param request   创建订单请求
     * @param cargoType
     * @return 创建成功的订单
     */
    @Transactional
    public Order createOrderWithAppointment(CreateOrderRequest request, CargoType cargoType) {
        // 查询发货仓信息
        var fromWarehouse = warehouseRepository.selectByCode(request.getFromWarehouseCode());
        if (fromWarehouse == null) {
            throw new ResourceNotFoundException("发货仓不存在: " + request.getFromWarehouseCode());
        }

        // 查询收货仓信息
        var toWarehouse = warehouseRepository.selectByCode(request.getToWarehouseCode());
        if (toWarehouse == null) {
            throw new ResourceNotFoundException("收货仓不存在: " + request.getToWarehouseCode());
        }

        // 创建订单并设置仓库信息
        Order order = new Order(request);
        // 确保订单有租户ID
        if (order.getTenantId() == null) {
            order.setTenantId(com.mascj.lalp.common.context.TenantContext.getTenantId());
        }
        
        order.setFromWarehouseName(fromWarehouse.getName());
        order.setToWarehouseName(toWarehouse.getName());
        order.setCargoType(cargoType.getName());
        order.setCargoTypeCode(cargoType.getCode());

        // 生成订单号
        String orderNo = generateOrderNumber();
        order.setOrderNo(orderNo);

        // 保存寄件人和收件人信息
        senderRepository.saveIfNotExists(request.getSenderName(), request.getSenderPhone(), request.getCargoContent(),
                order.getTenantId());
        recipientRepository.saveIfNotExists(request.getReceiverName(), request.getReceiverPhone(),
                request.getCargoContent(), order.getTenantId());

        // 保存订单
        Order savedOrder = orderRepository.save(order);

        // 发送预约寄件短信通知
        sendOrderPackageSms(savedOrder);

        return savedOrder;
    }

    /**
     * 生成订单号
     * 格式: LMWL-YYYYMMDD-XXXX (XXXX为当天订单序号，从0001开始)
     */
    private String generateOrderNumber() {
        // 获取当前日期
        String dateStr = LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyyMMdd"));

        // 查询当天已有订单数量
        LocalDateTime startOfDay = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0);
        LocalDateTime endOfDay = LocalDateTime.now().withHour(23).withMinute(59).withSecond(59);
        int orderCount = orderRepository.countByCreateTimeBetween(startOfDay, endOfDay) + 1;

        // 格式化为4位序号，不足前面补0
        String sequence = String.format("%04d", orderCount);

        return String.format("LMWL-%s-%s", dateStr, sequence);
    }

    /**
     * 发送预约寄件短信通知
     *
     * @param order 订单信息
     */
    private void sendOrderPackageSms(Order order) {
        try {
            log.info("准备发送预约寄件短信: orderId={}, senderPhone={}, sendCode={}",
                    order.getId(), order.getSenderPhone(), order.getSendCode());

            // 获取寄件人的用户ID
            List<Long> userIds = getUserIdsByPhone(order.getSenderPhone());

            if (userIds.isEmpty()) {
                log.warn("未找到手机号对应的用户: phone={}", order.getSenderPhone());
                return;
            }

            // 发送预约寄件短信（使用寄件码）
            boolean success = smsService.sendOrderPackageSms(
                    order.getTenantId(),
                    userIds,
                    order.getSendCode() // 使用订单的寄件码
            );

            if (success) {
                log.info("预约寄件短信发送成功: orderId={}, phone={}, sendCode={}",
                        order.getId(), order.getSenderPhone(), order.getSendCode());
            } else {
                log.error("预约寄件短信发送失败: orderId={}, phone={}, sendCode={}",
                        order.getId(), order.getSenderPhone(), order.getSendCode());
            }

        } catch (Exception e) {
            log.error("发送预约寄件短信异常: orderId={}, error={}", order.getId(), e.getMessage(), e);
        }
    }

    /**
     * 根据手机号获取用户ID列表
     *
     * @param phone 手机号
     * @return 用户ID列表
     */
    private List<Long> getUserIdsByPhone(String phone) {
        if (phone == null || phone.trim().isEmpty()) {
            log.warn("手机号为空，无法查询用户");
            return List.of();
        }

        try {
            // 根据手机号查询用户
            User user = userRepository.findByPhone(phone);

            if (user == null) {
                log.warn("未找到手机号对应的用户: phone={}", phone);
                return List.of();
            }

            // 检查用户状态
            if (!user.isEnabled()) {
                log.warn("用户状态不可用: phone={}, status={}", phone, user.getStatus());
                return List.of();
            }

            // 检查外部用户ID是否存在
            if (user.getOuterUserId() == null) {
                log.warn("用户缺少外部用户ID: phone={}, internalId={}, name={}", phone, user.getId(), user.getName());
                return List.of();
            }

            log.debug("找到用户: phone={}, internalId={}, outerUserId={}, name={}",
                    phone, user.getId(), user.getOuterUserId(), user.getName());
            return List.of(user.getOuterUserId()); // 返回外部用户ID，用于短信发送

        } catch (Exception e) {
            log.error("根据手机号查询用户失败: phone={}, error={}", phone, e.getMessage(), e);
            return List.of();
        }
    }

    /**
     * 根据订单ID查询配送任务
     * 
     * @param orderId 订单ID
     * @return 配送任务，如果不存在则返回null
     */
    public DeliveryTask findDeliveryTaskByOrderId(Long orderId) {
        if (orderId == null) {
            return null;
        }
        // 使用MyBatis-Plus的查询构造器构建查询条件
        return deliveryTaskRepository.selectOne(
                new LambdaQueryWrapper<DeliveryTask>()
                        .eq(DeliveryTask::getOrderId, orderId)
                        .orderByDesc(DeliveryTask::getCreateTime) // 获取最新的配送任务
                        .last("LIMIT 1"));
    }

    /**
     * 根据取件码查询订单
     * 
     * @return 订单信息，如果不存在则返回null
     */
    public Order findByPickupCode(OpenBoxForPickedUpRequest request) {
        LambdaQueryWrapper<Order> orderLambdaQueryWrapper = new LambdaQueryWrapper<>();
        orderLambdaQueryWrapper
                .eq(Order::getToWarehouseCode, request.getDeviceCode())
                .eq(Order::getPickupCode, request.getPickupCode());

        return orderRepository.selectOne(orderLambdaQueryWrapper);
    }
    /**
     * 根据发货码查询订单
     * @param request 包含设备编码和发货码的请求参数
     * @return 订单信息，如果不存在则返回null
     */
    public Order findBySendCode(@Valid OpenBoxForSendingRequest request) {
        LambdaQueryWrapper<Order> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Order::getFromWarehouseCode, request.getDeviceCode())
                .eq(Order::getSendCode, request.getSendCode());
        return orderRepository.selectOne(queryWrapper);
    }
    /**
     * 开箱操作
     * 
     * @param orderId 订单ID
     * @param userId  用户ID
     * @throws ResourceNotFoundException 当订单不存在时抛出
     * @throws IllegalStateException     当订单状态不允许开箱时抛出
     */
    @Transactional
    public void openBoxForPickedUp(Long orderId, Long userId) {
        // 1. 查询订单
        Order order = orderRepository.selectById(orderId);
        if (order == null) {
            throw new ResourceNotFoundException("Order not found with id: " + orderId);
        }

        // 2. 验证用户是否有权限操作
        if (!order.getUserId().equals(userId)) {
            throw new IllegalStateException("User " + userId + " is not authorized to open this box");
        }

        // 3. 验证订单状态是否允许开箱
        if (!order.getStatus().equals(OrderStatus.DELIVERY_SUCCESS.getCode())) {
            throw new IllegalStateException("Cannot open box for order with status: " + order.getStatus());
        }

        // 4. 更新订单状态为已取件
        orderRepository.updateStatusAndTime(orderId, OrderStatus.RECEIVED.getCode(), LocalDateTime.now());

        // 5. 记录开箱日志
        log.info("Box opened for order {} by user {} at {}", orderId, userId, LocalDateTime.now());
    }

    /**
     * 开箱寄件操作
     *
     * @param orderId 订单ID
     * @param userId  用户ID
     * @throws ResourceNotFoundException 当订单不存在时抛出
     * @throws IllegalStateException     当订单状态不允许开箱寄件时抛出
     */
    @Transactional
    public void openBoxForSending(Long orderId, Long userId) {
        // 1. 查询订单
        Order order = orderRepository.selectById(orderId);
        if (order == null) {
            throw new ResourceNotFoundException("Order not found with id: " + orderId);
        }

        // 2. 验证用户是否有权限操作
        if (!order.getUserId().equals(userId)) {
            throw new IllegalStateException("User " + userId + " is not authorized to open this box for sending");
        }

        // 3. 验证订单状态是否允许开箱寄件
        if (!order.getStatus().equals(OrderStatus.CREATED.getCode())) {
            throw new IllegalStateException("Cannot open box for sending with order status: " + order.getStatus());
        }

        // 4. 更新订单状态为待取件
        orderRepository.updateStatusAndTime(order.getId(), OrderStatus.TO_WAREHOUSE.getCode(), LocalDateTime.now());

        // 5. 记录开箱寄件日志
        log.info("Box opened for sending order {} by user {} at {}", orderId, userId, LocalDateTime.now());
    }

    public IPage<Order> pageOrders(OrderPageQuery query) {
        // 使用工具类创建分页对象
        Page<Order> page = PageUtils.createPage(query.getPageNum(), query.getPageSize());

        // 构建查询条件
        LambdaQueryWrapper<Order> queryWrapper = new LambdaQueryWrapper<>();

        // 使用工具类添加条件
        QueryUtils.addEqConditionIfNotNull(queryWrapper, query.getUserId(), Order::getUserId);

        // 如果没有userId但有phone，则根据手机号查询用户相关的订单
        if (query.getUserId() == null && StringUtils.isNotBlank(query.getPhone())) {
            // 构建手机号相关的查询条件：收件人手机号匹配
            queryWrapper.and(wrapper ->
                wrapper.or()
                       .eq(Order::getReceiverPhone, query.getPhone())
            );
        }

        // 根据订单类型进一步过滤
        if (StringUtils.isNotBlank(query.getOrderType()) && StringUtils.isNotBlank(query.getPhone())) {
            // 如果指定了订单类型，则进一步细化查询条件
            if ("PICKUP".equalsIgnoreCase(query.getOrderType())) {
                // 取件：只查询收件人手机号与当前用户相同的订单
                queryWrapper.and(wrapper -> wrapper.eq(Order::getReceiverPhone, query.getPhone()));
            } else if ("DELIVERY".equalsIgnoreCase(query.getOrderType())) {
                // 寄件：只查询寄件人手机号与当前用户相同的订单
                queryWrapper.and(wrapper -> wrapper.eq(Order::getSenderPhone, query.getPhone()));
            }
        }
        // 使用工具类添加模糊查询条件
        QueryUtils.addLikeConditionIfNotBlank(queryWrapper, query.getOrderNo(), Order::getOrderNo);
        QueryUtils.addLikeConditionIfNotBlank(queryWrapper, query.getSenderName(), Order::getSenderName);
        QueryUtils.addLikeConditionIfNotBlank(queryWrapper, query.getRecipientName(), Order::getReceiverName);
        
        // 添加对收件人手机号的查询条件
        QueryUtils.addLikeConditionIfNotBlank(queryWrapper, query.getRecipientPhone(), Order::getReceiverPhone);

        // 订单状态查询
        if (StringUtils.isNotBlank(query.getStatus())) {
            try {
                // 先尝试将状态解析为DeliveryTaskStatus
                DeliveryTaskStatus deliveryStatus = DeliveryTaskStatus.valueOf(query.getStatus());
                // 转换为OrderStatus
                OrderStatus orderStatus = OrderStatus.fromDeliveryTaskStatus(deliveryStatus);
                if (orderStatus != null) {
                    queryWrapper.eq(Order::getStatus, orderStatus.getCode());
                }
            } catch (IllegalArgumentException e) {
                // 如果不是DeliveryTaskStatus，尝试直接解析为OrderStatus
                try {
                    OrderStatus orderStatus = OrderStatus.valueOf(query.getStatus());
                    queryWrapper.eq(Order::getStatus, orderStatus.getCode());
                } catch (IllegalArgumentException ex) {
                    // 如果都失败，可能是直接传入的数字状态码
                    queryWrapper.eq(Order::getStatus, query.getStatus());
                }
            }
        }

        // 按创建时间倒序
        queryWrapper.orderByDesc(Order::getCreateTime);

        // 执行分页查询并返回
        return orderRepository.selectPage(page, queryWrapper);
    }

    /**
     * 标记订单为已签收
     * 
     * @param orderId 订单ID
     * @return 更新后的订单
     * @throws ResourceNotFoundException 当订单不存在时抛出
     * @throws IllegalStateException     当订单状态不允许标记为已签收时抛出
     */
    @Transactional
    public Order markAsReceived(Long orderId) {
        // 1. 查找订单
        Order order = orderRepository.selectById(orderId);
        if (order == null) {
            throw new ResourceNotFoundException("订单不存在: " + orderId);
        }

        // 2. 验证订单状态是否允许标记为已签收
        if (!"DELIVERED".equals(order.getStatus())) {
            throw new IllegalStateException("只有已投递状态的订单才能标记为已签收，当前状态: " + order.getStatus());
        }

        // 3. 更新订单状态为已签收
        order.setStatus("RECEIVED");
        order.setReceivedTime(LocalDateTime.now());

        // 4. 保存更新
        orderRepository.updateById(order);

        // 5. 更新配送任务表中的送达时间和总飞行时间
        DeliveryTask deliveryTask = deliveryTaskRepository.selectOne(
                new LambdaQueryWrapper<DeliveryTask>()
                        .eq(DeliveryTask::getOrderId, orderId)
                        .orderByDesc(DeliveryTask::getCreateTime)
                        .last("LIMIT 1"));
        
        if (deliveryTask != null) {
            LocalDateTime now = LocalDateTime.now();
            deliveryTask.setReceivedTime(now);
            
            // 计算总飞行时间（分钟）
            if (deliveryTask.getDepartureTime() != null && deliveryTask.getArrivalTime() != null) {
                long totalFlightMinutes = java.time.Duration.between(
                    deliveryTask.getDepartureTime(), 
                    deliveryTask.getArrivalTime()
                ).toMinutes();
                deliveryTask.setTotalFlightTime((int) totalFlightMinutes);
                deliveryTask.setDeliveryDuration((int) java.time.Duration.between(
                    deliveryTask.getCreateTime(),
                    now
                ).toMinutes());
            }
            
            deliveryTaskRepository.updateById(deliveryTask);
        }

        // 6. 记录操作日志
        log.info("标记订单已签收, orderId={}", orderId);

        return order;
    }



}