package com.mascj.lalp.application.service.sms;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 短信模板类型枚举
 */
@Getter
@AllArgsConstructor
public enum SmsTemplateType {

    /**
     * 预约寄件
     */
    ORDER_PACKAGE("ORDER_PACKAGE", "SMS_492045119", "预约寄件通知"),

    /**
     * 包裹到达
     */
    PACKAGE_ARRIVED("PACKAGE_ARRIVED", "SMS_492035118", "包裹到达通知");

    /**
     * 模板代码
     */
    private final String code;

    /**
     * 阿里云短信模板ID
     */
    private final String templateId;

    /**
     * 模板描述
     */
    private final String description;

    /**
     * 根据代码获取模板类型
     */
    public static SmsTemplateType getByCode(String code) {
        for (SmsTemplateType type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("未知的短信模板类型: " + code);
    }
}
