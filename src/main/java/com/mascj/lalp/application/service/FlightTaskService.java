package com.mascj.lalp.application.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mascj.lalp.domain.model.FlightTask;
import com.mascj.lalp.domain.model.FlightTaskStatus;
import com.mascj.lalp.domain.repository.FlightTaskRepository;
import com.mascj.lalp.interfaces.rest.backend.dto.FlightTaskResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 飞行任务服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FlightTaskService {

    private final FlightTaskRepository flightTaskRepository;

    /**
     * 分页查询飞行任务
     */
    public Page<FlightTaskResponse> getFlightTaskPage(int current, int size, String deviceSn,
                                                    FlightTaskStatus status, LocalDateTime startTime,
                                                    LocalDateTime endTime) {
        Page<FlightTask> page = new Page<>(current, size);
        Page<FlightTask> flightTaskPage = flightTaskRepository.findPage(page, deviceSn, status, startTime, endTime);

        // 转换为响应DTO
        Page<FlightTaskResponse> responsePage = new Page<>(current, size);
        responsePage.setTotal(flightTaskPage.getTotal());
        responsePage.setPages(flightTaskPage.getPages());
        responsePage.setCurrent(flightTaskPage.getCurrent());
        responsePage.setSize(flightTaskPage.getSize());

        List<FlightTaskResponse> responseList = flightTaskPage.getRecords().stream()
                .map(FlightTaskResponse::from)
                .collect(Collectors.toList());
        responsePage.setRecords(responseList);

        return responsePage;
    }

    /**
     * 根据ID查询飞行任务
     */
    public FlightTaskResponse getFlightTaskById(Long id) {
        return flightTaskRepository.findById(id)
                .map(FlightTaskResponse::from)
                .orElse(null);
    }

    /**
     * 根据配送任务ID查询飞行任务
     */
    public List<FlightTaskResponse> getFlightTasksByDeliveryTaskId(Long deliveryTaskId) {
        List<FlightTask> flightTasks = flightTaskRepository.findByDeliveryTaskId(deliveryTaskId);
        return flightTasks.stream()
                .map(FlightTaskResponse::from)
                .collect(Collectors.toList());
    }

    /**
     * 根据设备SN查询飞行任务
     */
    public List<FlightTaskResponse> getFlightTasksByDeviceSn(String deviceSn) {
        List<FlightTask> flightTasks = flightTaskRepository.findByDeviceSn(deviceSn);
        return flightTasks.stream()
                .map(FlightTaskResponse::from)
                .collect(Collectors.toList());
    }

    /**
     * 查询正在执行的飞行任务
     */
    public List<FlightTaskResponse> getExecutingFlightTasks() {
        List<FlightTask> flightTasks = flightTaskRepository.findExecutingTasks();
        return flightTasks.stream()
                .map(FlightTaskResponse::from)
                .collect(Collectors.toList());
    }

    /**
     * 飞行任务统计
     */
    public Map<String, Object> getFlightTaskStatistics() {
        Map<String, Object> statistics = new HashMap<>();
        
        // 统计各状态的任务数量
        for (FlightTaskStatus status : FlightTaskStatus.values()) {
            long count = flightTaskRepository.countByStatus(status);
            statistics.put(status.name().toLowerCase() + "Count", count);
        }
        
        // 总任务数
        long totalCount = flightTaskRepository.selectCount(null);
        statistics.put("totalCount", totalCount);
        
        // 今日任务数
        LocalDateTime todayStart = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0).withNano(0);
        LocalDateTime todayEnd = todayStart.plusDays(1);
        List<FlightTask> todayTasks = flightTaskRepository.findByTimeRange(todayStart, todayEnd);
        statistics.put("todayCount", todayTasks.size());
        
        // 成功率
        long successCount = flightTaskRepository.countByStatus(FlightTaskStatus.COMPLETED);
        double successRate = totalCount > 0 ? (double) successCount / totalCount * 100 : 0;
        statistics.put("successRate", Math.round(successRate * 100.0) / 100.0);
        
        return statistics;
    }

    /**
     * 更新飞行任务状态
     */
    @Transactional
    public FlightTaskResponse updateFlightTaskStatus(Long id, FlightTaskStatus status) {
        FlightTask flightTask = flightTaskRepository.selectById(id);
        if (flightTask == null) {
            throw new RuntimeException("飞行任务不存在: " + id);
        }
        
        // 更新状态
        switch (status) {
            case EXECUTING:
                flightTask.markAsExecuting();
                break;
            case COMPLETED:
                flightTask.markAsCompleted();
                break;
            case CANCELLED:
                flightTask.markAsCancelled();
                break;
            case FAILED:
                flightTask.markAsFailed("手动标记为失败", "MANUAL_FAILED");
                break;
            default:
                flightTask.setStatus(status);
                flightTask.setUpdateTime(LocalDateTime.now());
                break;
        }
        
        // 保存更新
        flightTaskRepository.updateById(flightTask);
        
        log.info("飞行任务状态已更新: id={}, status={}", id, status);
        
        return FlightTaskResponse.from(flightTask);
    }

    /**
     * 根据飞控系统任务ID查询飞行任务
     */
    public FlightTaskResponse getFlightTaskByFlightTaskId(String flightTaskId) {
        return flightTaskRepository.findByFlightTaskId(flightTaskId)
                .map(FlightTaskResponse::from)
                .orElse(null);
    }

    /**
     * 查询所有飞行任务
     */
    public List<FlightTaskResponse> getAllFlightTasks() {
        List<FlightTask> flightTasks = flightTaskRepository.findAll();
        return flightTasks.stream()
                .map(FlightTaskResponse::from)
                .collect(Collectors.toList());
    }

    /**
     * 根据状态查询飞行任务
     */
    public List<FlightTaskResponse> getFlightTasksByStatus(FlightTaskStatus status) {
        List<FlightTask> flightTasks = flightTaskRepository.findByStatus(status);
        return flightTasks.stream()
                .map(FlightTaskResponse::from)
                .collect(Collectors.toList());
    }

    /**
     * 统计设备的飞行任务数量
     */
    public long countFlightTasksByDeviceSn(String deviceSn) {
        return flightTaskRepository.countByDeviceSn(deviceSn);
    }
}
