package com.mascj.lalp.application.service.sms;

import com.alibaba.fastjson.JSON;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 短信发送服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SmsService {

    private final RabbitTemplate rabbitTemplate;
    
    /**
     * RabbitMQ消息通道
     */
    private static final String SMS_CHANNEL = "lup-msg-passageway-server.lupChannel";

    /**
     * 发送预约寄件短信
     * 
     * @param tenantId 租户ID
     * @param userIds 用户ID列表
     * @param senderCode 寄件码
     * @return 是否发送成功
     */
    public boolean sendOrderPackageSms(Long tenantId, List<Long> userIds, String senderCode) {
        log.info("发送预约寄件短信: tenantId={}, userIds={}, senderCode={}", tenantId, userIds, senderCode);
        
        // 构建短信参数
        Map<String, Object> params = new HashMap<>();
        params.put("num", senderCode); // 寄件码
        
        // 发送短信
        return sendSms(tenantId, userIds, SmsTemplateType.ORDER_PACKAGE, params);
    }

    /**
     * 发送包裹到达短信
     * 
     * @param tenantId 租户ID
     * @param userIds 用户ID列表
     * @param warehouseName 物流仓名称
     * @param pickupCode 取件码
     * @return 是否发送成功
     */
    public boolean sendPackageArrivedSms(Long tenantId, List<Long> userIds, String warehouseName, String pickupCode) {
        log.info("发送包裹到达短信: tenantId={}, userIds={}, warehouseName={}, pickupCode={}", 
                tenantId, userIds, warehouseName, pickupCode);
        
        // 构建短信参数
        Map<String, Object> params = new HashMap<>();
        params.put("address", warehouseName); // 物流仓名称
        params.put("num", pickupCode); // 取件码
        
        // 发送短信
        return sendSms(tenantId, userIds, SmsTemplateType.PACKAGE_ARRIVED, params);
    }

    /**
     * 通用短信发送方法
     * 
     * @param tenantId 租户ID
     * @param userIds 用户ID列表
     * @param templateType 短信模板类型
     * @param params 短信参数
     * @return 是否发送成功
     */
    private boolean sendSms(Long tenantId, List<Long> userIds, SmsTemplateType templateType, Map<String, Object> params) {
        try {
            // 构建消息对象
            MsgTemplateVO msgTemplateVO = new MsgTemplateVO();
            msgTemplateVO.setTenantId(tenantId);
            msgTemplateVO.setUserIds(userIds);
            msgTemplateVO.setType(templateType.getCode());
            msgTemplateVO.setSourceType(2); // 来源：2-配送系统
            msgTemplateVO.setParams(params);

            // 记录发送日志
            String jsonString = JSON.toJSONString(msgTemplateVO);
            log.info("发送短信消息到MQ: channel={}, message={}", SMS_CHANNEL, jsonString);

            // 发送到RabbitMQ
            rabbitTemplate.convertAndSend(SMS_CHANNEL, msgTemplateVO);
            
            log.info("短信消息发送成功: templateType={}, tenantId={}, userCount={}", 
                    templateType.getDescription(), tenantId, userIds.size());
            
            return true;
            
        } catch (Exception e) {
            log.error("发送短信失败: templateType={}, tenantId={}, error={}", 
                    templateType.getDescription(), tenantId, e.getMessage(), e);
            return false;
        }
    }
}
