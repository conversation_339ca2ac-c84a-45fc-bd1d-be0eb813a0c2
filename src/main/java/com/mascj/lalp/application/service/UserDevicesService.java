package com.mascj.lalp.application.service;

import com.mascj.lalp.common.context.TenantContext;
import com.mascj.lalp.domain.model.*;
import com.mascj.lalp.interfaces.rest.backend.dto.DeviceInfo;
import com.mascj.lalp.interfaces.rest.backend.dto.DeviceStatistics;
import com.mascj.lalp.interfaces.rest.backend.dto.UserDevicesResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 用户设备服务
 * 负责查询用户的所有设备信息
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserDevicesService {

    private final WarehouseService warehouseService;
    private final DroneService droneService;
    private final InventoryService inventoryService;

    /**
     * 获取当前用户的所有设备信息
     * 包括仓库、无人机和库存信息
     *
     * @return 用户的所有设备信息
     */
    public UserDevicesResponse getCurrentUserDevices() {
        log.info("查询当前用户的所有设备信息");
        
        // 获取当前租户ID
        Long tenantId = TenantContext.getTenantId();
        log.debug("当前租户ID: {}", tenantId);
        
        // 查询当前租户下的所有仓库
        List<Warehouse> warehouses = warehouseService.getWarehousesByTenantId(tenantId);
        log.debug("查询到仓库数量: {}", warehouses.size());
        
        // 查询当前租户下的所有无人机
        List<Drone> drones = droneService.getDronesByTenantId(tenantId);
        log.debug("查询到无人机数量: {}", drones.size());
        
        // 查询当前租户下的所有库存记录
        List<Inventory> inventories = getInventoriesByTenant(warehouses);
        log.debug("查询到库存记录数量: {}", inventories.size());

        // 转换为统一的设备信息列表
        List<DeviceInfo> allDevices = convertToDeviceInfoList(warehouses, drones, inventories);

        // 生成设备统计信息
        DeviceStatistics statistics = generateDeviceStatistics(warehouses, drones, inventories);

        // 封装响应数据
        return UserDevicesResponse.builder()
                .warehouses(warehouses)
                .drones(drones)
                .inventories(inventories)
                .allDevices(allDevices)
                .statistics(statistics)
                .build();
    }

    /**
     * 根据仓库列表获取库存记录
     * @param warehouses 仓库列表
     * @return 库存记录列表
     */
    private List<Inventory> getInventoriesByTenant(List<Warehouse> warehouses) {
        if (warehouses.isEmpty()) {
            return List.of();
        }
        
        // 获取所有仓库ID
        List<Long> warehouseIds = warehouses.stream()
                .map(Warehouse::getId)
                .collect(Collectors.toList());
        
        // 根据仓库ID查询库存记录
        return inventoryService.getInventoriesByWarehouseIds(warehouseIds);
    }

    /**
     * 将各类设备转换为统一的设备信息列表
     * @param warehouses 仓库列表
     * @param drones 无人机列表
     * @param inventories 库存列表
     * @return 统一的设备信息列表
     */
    private List<DeviceInfo> convertToDeviceInfoList(List<Warehouse> warehouses,
                                                    List<Drone> drones,
                                                    List<Inventory> inventories) {
        List<DeviceInfo> allDevices = new ArrayList<>();

        // 转换仓库信息
        warehouses.forEach(warehouse -> {
            DeviceInfo deviceInfo = DeviceInfo.builder()
                    .id(warehouse.getId())
                    .name(warehouse.getName())
                    .code(warehouse.getCode())
                    .deviceType(DeviceType.WAREHOUSE)
                    .status(warehouse.getStatus() != null ? warehouse.getStatus().name() : "UNKNOWN")
                    .location(warehouse.getAddress())
                    .createTime(warehouse.getCreateTime())
                    .tenantId(warehouse.getTenantId())
                    .extendInfo(warehouse)
                    .build();
            allDevices.add(deviceInfo);
        });

        // 转换无人机信息
        drones.forEach(drone -> {
            DeviceInfo deviceInfo = DeviceInfo.builder()
                    .id(drone.getId())
                    .name(drone.getName())
                    .code(drone.getDroneId())
                    .deviceType(DeviceType.DRONE)
                    .status(drone.getStatus())
                    .location(drone.getLocation())
                    .tenantId(drone.getTenantId())
                    .lastUpdateTime(drone.getLastCommunicationTime())
                    .extendInfo(drone)
                    .build();
            allDevices.add(deviceInfo);
        });

        // 转换库存信息
        inventories.forEach(inventory -> {
            DeviceInfo deviceInfo = DeviceInfo.builder()
                    .id(inventory.getId())
                    .name(inventory.getWarehouseName())
                    .code(inventory.getCode())
                    .deviceType(DeviceType.INVENTORY)
                    .status("ACTIVE")
                    .createTime(inventory.getCreateTime())
                    .lastUpdateTime(inventory.getCheckTime())
                    .extendInfo(inventory)
                    .build();
            allDevices.add(deviceInfo);
        });

        return allDevices;
    }

    /**
     * 生成设备统计信息
     * @param warehouses 仓库列表
     * @param drones 无人机列表
     * @param inventories 库存列表
     * @return 设备统计信息
     */
    private DeviceStatistics generateDeviceStatistics(List<Warehouse> warehouses,
                                                     List<Drone> drones,
                                                     List<Inventory> inventories) {
        // 统计仓库状态
        int onlineWarehouses = 0;
        int offlineWarehouses = 0;
        for (Warehouse warehouse : warehouses) {
            if (warehouse.getStatus() == WarehouseStatus.ONLINE) {
                onlineWarehouses++;
            } else {
                offlineWarehouses++;
            }
        }

        // 统计无人机状态
        int onlineDrones = 0;
        int offlineDrones = 0;
        int workingDrones = 0;
        int maintenanceDrones = 0;

        for (Drone drone : drones) {
            String status = drone.getStatus();
            if ("ONLINE".equals(status)) {
                onlineDrones++;
            } else if ("OFFLINE".equals(status)) {
                offlineDrones++;
            } else if ("WORKING".equals(status) || "MISSION".equals(status)) {
                workingDrones++;
            } else if ("MAINTENANCE".equals(status)) {
                maintenanceDrones++;
            } else {
                offlineDrones++; // 默认归类为离线
            }
        }

        return DeviceStatistics.builder()
                .totalWarehouses(warehouses.size())
                .onlineWarehouses(onlineWarehouses)
                .offlineWarehouses(offlineWarehouses)
                .totalDrones(drones.size())
                .onlineDrones(onlineDrones)
                .offlineDrones(offlineDrones)
                .workingDrones(workingDrones)
                .maintenanceDrones(maintenanceDrones)
                .totalInventories(inventories.size())
                .totalDevices(warehouses.size() + drones.size() + inventories.size())
                .build();
    }
}
