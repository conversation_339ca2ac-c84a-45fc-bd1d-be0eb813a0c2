package com.mascj.lalp.application.service;

import com.mascj.lalp.domain.model.DeliveryTask;
import com.mascj.lalp.interfaces.feign.FlightControlFeign;
import com.mascj.lalp.interfaces.rest.backend.dto.CreateFlightTaskRequest;
import com.mascj.lalp.interfaces.rest.backend.dto.CreateFlightTaskResponse;
import com.mascj.lalp.interfaces.rest.backend.dto.RemoteFlightTaskResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * 飞控系统服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FlightControlService {

    private final FlightControlFeign flightControlFeign;

    @Value("${flight-control.default-altitude:200}")
    private Double defaultAltitude;

    @Value("${flight-control.auth-token:}")
    private String defaultAuthToken;

    /**
     * 为配送任务创建飞行任务
     *
     * @param deliveryTask 配送任务
     * @param authToken 认证令牌（可选，如果为空则使用默认值）
     * @return 创建结果
     */
    public CreateFlightTaskResponse createFlightTaskForDelivery(DeliveryTask deliveryTask, String authToken) {
        try {
            log.info("开始为配送任务创建飞行任务: taskId={}, droneId={}",
                    deliveryTask.getId(), deliveryTask.getDroneId());

            // 1. 直接使用配送任务中的droneId作为deviceSn
            String deviceSn = deliveryTask.getDroneId();
            if (deviceSn == null || deviceSn.trim().isEmpty()) {
                return CreateFlightTaskResponse.failure(
                    "设备SN缺失",
                    "DEVICE_SN_MISSING",
                    "配送任务中未指定设备SN"
                );
            }

            // 2. 构建飞行任务请求
            CreateFlightTaskRequest request = CreateFlightTaskRequest.fromDeliveryTask(
                deliveryTask,
                deviceSn,
                defaultAltitude
            );

            // 3. 使用提供的token或默认token
            String token = (authToken != null && !authToken.trim().isEmpty()) ? authToken : defaultAuthToken;
            if (token == null || token.trim().isEmpty()) {
                return CreateFlightTaskResponse.failure(
                    "认证令牌缺失",
                    "AUTH_TOKEN_MISSING",
                    "请提供有效的认证令牌"
                );
            }

            // 4. 调用远程飞控接口（先用字符串方式调试）
            log.info("调用飞控接口创建任务: deviceSn={}, taskName={}, alt={}, targetStoreSn={}, startStoreSn={}, speed={}, finish={}",
                    request.getDeviceSn(), request.getName(), request.getAlt(),
                    request.getTargetStoreSn(), request.getStartStoreSn(),
                    request.getSpeed(), request.getFinish());

            try {
                // 创建ObjectMapper实例，用于JSON序列化和反序列化
                com.fasterxml.jackson.databind.ObjectMapper objectMapper = new com.fasterxml.jackson.databind.ObjectMapper();

                // 记录发送的完整请求数据
                String requestJson = objectMapper.writeValueAsString(request);
                log.info("发送给飞控接口的完整请求JSON: {}", requestJson);

                // 调用远程接口获取原始响应
                String rawResponse = flightControlFeign.createFlightTask(token, request);
                log.info("远程飞控接口原始响应: {}", rawResponse);

                // 检查响应内容
                if (rawResponse.trim().startsWith("<")) {
                    // HTML响应，通常是错误页面
                    log.error("远程服务返回HTML页面，可能是认证失败或服务错误");
                    return CreateFlightTaskResponse.failure(
                        "远程服务返回错误页面",
                        "HTML_RESPONSE",
                        "响应内容: " + (rawResponse.length() > 500 ? rawResponse.substring(0, 500) + "..." : rawResponse)
                    );
                }

                // 尝试解析JSON响应
                RemoteFlightTaskResponse remoteResponse = objectMapper.readValue(rawResponse, RemoteFlightTaskResponse.class);

                log.info("解析后的响应: code={}, msg={}, success={}, data={}",
                        remoteResponse.getCode(), remoteResponse.getMsg(),
                        remoteResponse.getSuccess(), remoteResponse.getData());

                // 转换为统一的响应格式
                CreateFlightTaskResponse response = remoteResponse.toCreateFlightTaskResponse(
                        request.getDeviceSn(), request.getName());

                if (response.getSuccess()) {
                    log.info("飞行任务创建成功: taskId={}, flightTaskId={}",
                            deliveryTask.getId(), response.getFlightTaskId());
                } else {
                    log.warn("飞行任务创建失败: taskId={}, error={}",
                            deliveryTask.getId(), response.getMessage());
                }

                return response;

            } catch (Exception e) {
                log.error("调用远程飞控接口异常", e);
                return CreateFlightTaskResponse.failure(
                    "调用远程接口失败: " + e.getMessage(),
                    "REMOTE_CALL_ERROR",
                    e.toString()
                );
            }

        } catch (Exception e) {
            log.error("创建飞行任务异常: taskId={}", deliveryTask.getId(), e);
            return CreateFlightTaskResponse.failure(
                "创建飞行任务失败: " + e.getMessage(),
                "SYSTEM_ERROR",
                e.toString()
            );
        }
    }



    /**
     * 快速创建飞行任务（使用默认参数）
     */
    public CreateFlightTaskResponse createFlightTask(DeliveryTask deliveryTask) {
        return createFlightTaskForDelivery(deliveryTask, null);
    }


}
