package com.mascj.lalp.application.service;

import com.mascj.lalp.domain.model.DeliveryTask;
import com.mascj.lalp.interfaces.feign.FlightControlFeign;
import com.mascj.lalp.interfaces.rest.backend.dto.CreateFlightTaskRequest;
import com.mascj.lalp.interfaces.rest.backend.dto.CreateFlightTaskResponse;
import com.mascj.lalp.interfaces.rest.backend.dto.DroneHookControlRequest;
import com.mascj.lalp.interfaces.rest.backend.dto.DroneHookControlResponse;
import com.mascj.lalp.interfaces.rest.backend.dto.RemoteFlightTaskResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.util.StringUtils;
import com.mascj.lalp.infrastructure.common.security.SecUtil;

import jakarta.servlet.http.HttpServletRequest;

/**
 * 飞控系统服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FlightControlService {

    private final FlightControlFeign flightControlFeign;

    @Value("${flight-control.default-altitude:200}")
    private Double defaultAltitude;

    @Value("${flight-control.auth-token:}")
    private String defaultAuthToken;

    /**
     * 为配送任务创建飞行任务
     *
     * @param deliveryTask 配送任务
     * @param authToken 认证令牌（可选，如果为空则使用默认值）
     * @return 创建结果
     */
    public CreateFlightTaskResponse createFlightTaskForDelivery(DeliveryTask deliveryTask, String authToken) {
        try {
            log.info("开始为配送任务创建飞行任务: taskId={}, droneId={}",
                    deliveryTask.getId(), deliveryTask.getDroneId());

            // 1. 直接使用配送任务中的droneId作为deviceSn
            String deviceSn = deliveryTask.getDroneId();
            if (deviceSn == null || deviceSn.trim().isEmpty()) {
                return CreateFlightTaskResponse.failure(
                    "设备SN缺失",
                    "DEVICE_SN_MISSING",
                    "配送任务中未指定设备SN"
                );
            }

            // 2. 构建飞行任务请求
            CreateFlightTaskRequest request = CreateFlightTaskRequest.fromDeliveryTask(
                deliveryTask,
                deviceSn,
                defaultAltitude
            );

            // 3. 使用提供的token或默认token
            String token = (authToken != null && !authToken.trim().isEmpty()) ? authToken : defaultAuthToken;
            if (token == null || token.trim().isEmpty()) {
                return CreateFlightTaskResponse.failure(
                    "认证令牌缺失",
                    "AUTH_TOKEN_MISSING",
                    "请提供有效的认证令牌"
                );
            }

            // 4. 调用远程飞控接口（先用字符串方式调试）
            log.info("调用飞控接口创建任务: deviceSn={}, taskName={}, alt={}, targetStoreSn={}, startStoreSn={}, speed={}, finish={}",
                    request.getDeviceSn(), request.getName(), request.getAlt(),
                    request.getTargetStoreSn(), request.getStartStoreSn(),
                    request.getSpeed(), request.getFinish());

            try {
                // 创建ObjectMapper实例，用于JSON序列化和反序列化
                com.fasterxml.jackson.databind.ObjectMapper objectMapper = new com.fasterxml.jackson.databind.ObjectMapper();

                // 记录发送的完整请求数据
                String requestJson = objectMapper.writeValueAsString(request);
                log.info("发送给飞控接口的完整请求JSON: {}", requestJson);

                // 调用远程接口获取原始响应
                String rawResponse = flightControlFeign.createFlightTask(token, request);
                log.info("远程飞控接口原始响应: {}", rawResponse);

                // 检查响应内容
                if (rawResponse.trim().startsWith("<")) {
                    // HTML响应，通常是错误页面
                    log.error("远程服务返回HTML页面，可能是认证失败或服务错误");
                    return CreateFlightTaskResponse.failure(
                        "远程服务返回错误页面",
                        "HTML_RESPONSE",
                        "响应内容: " + (rawResponse.length() > 500 ? rawResponse.substring(0, 500) + "..." : rawResponse)
                    );
                }

                // 尝试解析JSON响应
                RemoteFlightTaskResponse remoteResponse = objectMapper.readValue(rawResponse, RemoteFlightTaskResponse.class);

                log.info("解析后的响应: code={}, msg={}, success={}, data={}",
                        remoteResponse.getCode(), remoteResponse.getMsg(),
                        remoteResponse.getSuccess(), remoteResponse.getData());

                // 转换为统一的响应格式
                CreateFlightTaskResponse response = remoteResponse.toCreateFlightTaskResponse(
                        request.getDeviceSn(), request.getName());

                if (response.getSuccess()) {
                    log.info("飞行任务创建成功: taskId={}, flightTaskId={}",
                            deliveryTask.getId(), response.getFlightTaskId());
                } else {
                    log.warn("飞行任务创建失败: taskId={}, error={}",
                            deliveryTask.getId(), response.getMessage());
                }

                return response;

            } catch (Exception e) {
                log.error("调用远程飞控接口异常", e);
                return CreateFlightTaskResponse.failure(
                    "调用远程接口失败: " + e.getMessage(),
                    "REMOTE_CALL_ERROR",
                    e.toString()
                );
            }

        } catch (Exception e) {
            log.error("创建飞行任务异常: taskId={}", deliveryTask.getId(), e);
            return CreateFlightTaskResponse.failure(
                "创建飞行任务失败: " + e.getMessage(),
                "SYSTEM_ERROR",
                e.toString()
            );
        }
    }



    /**
     * 快速创建飞行任务（使用默认参数）
     */
    public CreateFlightTaskResponse createFlightTask(DeliveryTask deliveryTask) {
        return createFlightTaskForDelivery(deliveryTask, null);
    }

    /**
     * 无人机抓钩控制
     *
     * @param request 抓钩控制请求
     * @param authToken 认证令牌（可选，如果为空则使用默认值）
     * @return 控制结果
     */
    public DroneHookControlResponse droneHookControl(DroneHookControlRequest request, String authToken) {
        try {
            log.info("开始执行无人机抓钩控制: deviceSn={}, indexList={}",
                    request.getDeviceSn(), request.getIndexList());

            // 1. 验证设备SN
            String deviceSn = request.getDeviceSn();
            if (deviceSn == null || deviceSn.trim().isEmpty()) {
                return DroneHookControlResponse.failure("设备SN缺失", 400);
            }

            // 2. 验证开关状态列表
            if (request.getIndexList() == null || request.getIndexList().size() != 4) {
                return DroneHookControlResponse.failure("开关状态列表必须包含4个元素", 400);
            }

            // 3. 验证开关状态值（只能是0或1）
            for (Integer index : request.getIndexList()) {
                if (index == null || (index != 0 && index != 1)) {
                    return DroneHookControlResponse.failure("开关状态值只能是0或1", 400);
                }
            }

            // 4. 使用提供的token、当前请求的token或默认token
            String token = authToken;
            if (token == null || token.trim().isEmpty()) {
                token = getCurrentUserAuthToken();
            }
            if (token == null || token.trim().isEmpty()) {
                token = defaultAuthToken;
            }
            if (token == null || token.trim().isEmpty()) {
                return DroneHookControlResponse.failure("认证令牌缺失", 401);
            }

            // 5. 调用远程飞控接口
            log.info("调用飞控接口执行抓钩控制: deviceSn={}, indexList={}",
                    request.getDeviceSn(), request.getIndexList());

            try {
                // 创建ObjectMapper实例，用于JSON序列化和反序列化
                com.fasterxml.jackson.databind.ObjectMapper objectMapper = new com.fasterxml.jackson.databind.ObjectMapper();

                // 记录发送的完整请求数据
                String requestJson = objectMapper.writeValueAsString(request);
                log.info("发送给飞控接口的完整抓钩控制请求JSON: {}", requestJson);

                // 调用远程接口获取原始响应
                String rawResponse = flightControlFeign.droneHookControl(token, request);
                log.info("远程飞控接口抓钩控制原始响应: {}", rawResponse);

                // 检查响应内容
                if (rawResponse.trim().startsWith("<")) {
                    // HTML响应，通常是错误页面
                    log.error("远程服务返回HTML页面，可能是认证失败或服务错误");
                    return DroneHookControlResponse.failure("远程服务返回错误页面", 500);
                }

                // 尝试解析JSON响应
                DroneHookControlResponse response = objectMapper.readValue(rawResponse, DroneHookControlResponse.class);

                log.info("解析后的抓钩控制响应: code={}, msg={}, success={}",
                        response.getCode(), response.getMsg(), response.getSuccess());

                if (Boolean.TRUE.equals(response.getSuccess()) && response.getCode() == 200) {
                    log.info("无人机抓钩控制成功: deviceSn={}", request.getDeviceSn());
                    return response;
                } else {
                    log.warn("无人机抓钩控制失败: deviceSn={}, error={}",
                            request.getDeviceSn(), response.getMsg());
                    return response;
                }

            } catch (Exception e) {
                log.error("调用远程飞控接口异常", e);
                return DroneHookControlResponse.failure("调用远程接口失败: " + e.getMessage(), 500);
            }

        } catch (Exception e) {
            log.error("无人机抓钩控制异常: deviceSn={}", request.getDeviceSn(), e);
            return DroneHookControlResponse.failure("抓钩控制失败: " + e.getMessage(), 500);
        }
    }

    /**
     * 获取当前用户的认证令牌
     * @return 认证令牌，如果获取失败则返回null
     */
    private String getCurrentUserAuthToken() {
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes != null) {
                HttpServletRequest request = attributes.getRequest();

                // 首先尝试从 liangma-auth header 获取 token
                String token = request.getHeader("liangma-auth");
                if (StringUtils.hasText(token)) {
                    return token;
                }

                // 如果没有找到，尝试从 Liangma-Auth header 获取 token
                token = request.getHeader(SecUtil.LIANGMA_TOKEN);
                if (StringUtils.hasText(token)) {
                    return token;
                }

                // 如果没有找到，尝试从 Delivery-Auth header 获取 token
                token = request.getHeader(SecUtil.HEADER_TOKEN);
                if (StringUtils.hasText(token)) {
                    return token;
                }
            }
        } catch (Exception e) {
            log.warn("获取当前用户认证令牌失败", e);
        }
        return null;
    }

}
