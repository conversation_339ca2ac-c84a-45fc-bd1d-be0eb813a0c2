package com.mascj.lalp.application.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mascj.lalp.application.service.sms.SmsService;
import com.mascj.lalp.common.interceptor.DistanceCalculator;
import com.mascj.lalp.common.util.QueryUtils;
import com.mascj.lalp.domain.exception.ResourceNotFoundException;
import com.mascj.lalp.domain.model.*;
import com.mascj.lalp.domain.repository.*;
import com.mascj.lalp.infrastructure.common.api.PageResult;
import com.mascj.lalp.infrastructure.common.security.SecUtil;
import com.mascj.lalp.interfaces.rest.backend.dto.*;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 配送任务服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DeliveryTaskService {
    private final DeliveryTaskRepository deliveryTaskRepository;
    private final OrderRepository orderRepository;
    private final CargoTypeRepository cargoTypeRepository;
    private final WarehouseRepository warehouseRepository;
    private final DroneRepository droneRepository;
    private final FlightControlService flightControlService;
    private final SmsService smsService;
    private final UserRepository userRepository;
    /**
     * 根据预约创建配送任务
     * @param droneId 预约ID
     * @return 创建的配送任务
     * @throws ResourceNotFoundException 当订单不存在时抛出
     */
    @Transactional
    public DeliveryTask createTaskFromOrder(Order order,String droneId) throws Exception {
        // 1. 验证订单信息
        if (order == null) {
            throw new Exception("订单不存在");
        }

        // 2. 使用构造函数创建配送任务
        DeliveryTask task = new DeliveryTask(order,droneId);

        // 7. 保存配送任务
        deliveryTaskRepository.insert(task);

        // 8. 更新订单状态为"已创建配送任务"
        orderRepository.updateById(order.taskCreated());

        // 9. 发送预约寄件短信通知
        sendOrderPackageSms(task);

        return task;
    }

    /**
     * 获取配送任务列表
     * @return 所有配送任务
     */
    public List<DeliveryTask> getTaskList() {
        return deliveryTaskRepository.findAll();
    }

    /**
     * 根据无人机ID查找正在执行的任务
     * @param droneId 无人机ID
     * @return 正在执行的任务列表
     */
    public List<DeliveryTask> getTasksByDroneId(String droneId) {
        if (droneId == null || droneId.trim().isEmpty()) {
            return List.of();
        }

        LambdaQueryWrapper<DeliveryTask> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DeliveryTask::getDroneId, droneId)
                .in(DeliveryTask::getStatus,
                        DeliveryTaskStatus.DELIVERING,
                        DeliveryTaskStatus.PICKING_UP,
                        DeliveryTaskStatus.DRONE_READY)
                .orderByDesc(DeliveryTask::getCreateTime);

        return deliveryTaskRepository.selectList(queryWrapper);
    }

    /**
     * 删除配送任务
     * @param taskId 任务ID
     */
    public void deleteTask(Long taskId) {
        deliveryTaskRepository.deleteById(taskId);
    }


    /**
     * 终止配送任务
     * 只有处于待处理(PENDING)或取货中(PICKING_UP)状态的任务才能被取消
     *
     * @param taskId 任务ID
     * @throws ResourceNotFoundException 当任务不存在时抛出
     * @throws IllegalStateException 当任务状态不允许取消时抛出
     */
    @Transactional
    public void cancelTask(Long taskId) {
        // 1. 查找任务
        DeliveryTask task = deliveryTaskRepository.selectById(taskId);
        if (task == null) {
            throw new ResourceNotFoundException("Delivery task not found with id: " + taskId);
        }

        // 2. 验证任务状态是否允许取消
        if (task.getStatus() != DeliveryTaskStatus.PENDING &&
                task.getStatus() != DeliveryTaskStatus.PICKING_UP) {
            throw new IllegalStateException("Cannot cancel task with status: " + task.getStatus() +
                    ". Only PENDING or PICKING_UP tasks can be cancelled.");
        }

        // 3. 更新任务状态为已取消
        task.setStatus(DeliveryTaskStatus.CANCELLED);

        // 4. 如果无人机正在执行此任务，需要释放无人机
        if (task.getDroneId() != null) {
            // TODO: 调用无人机服务释放无人机
            log.debug("Releasing drone {} from cancelled task {}", task.getDroneId(), taskId);
        }

        // 5. 保存更新
        deliveryTaskRepository.updateById(task);

        // 6. 记录操作日志
        log.info("Delivery task {} has been cancelled", taskId);
    }

    /**
     * 签收配送任务
     * @param taskId 任务ID
     * @throws ResourceNotFoundException 当任务不存在时抛出
     * @throws IllegalStateException 当任务状态不允许签收时抛出
     */
    @Transactional
    public void completeTask(Long taskId) {
        // 1. 查找任务
        DeliveryTask task = deliveryTaskRepository.selectById(taskId);
        if (task == null) {
            throw new ResourceNotFoundException("Delivery task not found with id: " + taskId);
        }

        // 2. 验证任务状态是否允许签收
        if (task.getStatus() != DeliveryTaskStatus.DELIVERING) {
            throw new IllegalStateException("Cannot complete task with status: " + task.getStatus());
        }

        // 3. 更新任务状态和送达时间
        task.setStatus(DeliveryTaskStatus.DELIVERED);
        task.setDeliveryTime(LocalDateTime.now());

        // 4. 保存更新
        deliveryTaskRepository.updateById(task);

        // 5. 记录操作日志
        log.info("Delivery task {} has been marked as delivered at {}", taskId, task.getDeliveryTime());
    }

    /**
     * 获取配送任务详情
     * @param taskId 任务ID
     * @return 配送任务详情
     */
    public DeliveryTask getTaskDetail(Long taskId) {
        return deliveryTaskRepository.findById(taskId).orElse(null);
    }

    /**
     * 获取配送任务详情（包含订单信息）
     * @param taskId 任务ID
     * @return 配送任务详情，如果不存在则返回null
     */
    public DeliveryTask getTaskDetailWithOrder(Long taskId) {
        // 1. 查询配送任务
        DeliveryTask task = deliveryTaskRepository.findById(taskId).orElse(null);
        if (task == null) {
            return null;
        }

        // 2. 如果任务有关联的订单ID，查询订单信息并补充到任务中
        if (task.getOrderId() != null) {
            Order order = orderRepository.findById(task.getOrderId());
            if (order != null) {
                // 设置订单号
                task.setOrderNo(order.getOrderNo());

                // 补充寄件人信息（从订单中获取）- 飞行计划创建人就是寄件人
                task.setCreatorName(order.getSenderName());
                task.setCreatorPhone(order.getSenderPhone());

                // 如果任务中没有收件人信息，从订单中补充
                if (task.getReceiverName() == null) {
                    task.setReceiverName(order.getReceiverName());
                }
                if (task.getReceiverPhone() == null) {
                    task.setReceiverPhone(order.getReceiverPhone());
                }

                // 补充货物信息
                if (task.getCargoType() == null) {
                    task.setCargoType(order.getCargoType());
                }
                if (task.getCargoTypeCode() == null) {
                    task.setCargoTypeCode(order.getCargoTypeCode());
                }
                if (task.getCargoContent() == null) {
                    task.setCargoContent(order.getCargoContent());
                }
                if (task.getCargoWeight() == null) {
                    task.setCargoWeight(order.getCargoWeight());
                }

                // 补充仓库信息
                if (task.getDeparturePoint() == null) {
                    task.setDeparturePoint(order.getFromWarehouseCode());
                }
                if (task.getArrivalPoint() == null) {
                    task.setArrivalPoint(order.getToWarehouseCode());
                }

                log.info("成功关联订单信息: taskId={}, orderId={}, orderNo={}, senderName={}",
                        taskId, order.getId(), order.getOrderNo(), order.getSenderName());
            }
        }

        // 3. 补充仓库名称信息
        enrichWarehouseNames(task);

        return task;
    }

    /**
     * 补充仓库名称信息
     * @param task 配送任务
     */
    private void enrichWarehouseNames(DeliveryTask task) {
        try {
            // 查询发货仓库名称
            if (StringUtils.isNotBlank(task.getDeparturePoint())) {
                Warehouse departureWarehouse = warehouseRepository.selectByCode(task.getDeparturePoint());
                if (departureWarehouse != null) {
                    task.setDeparturePointName(departureWarehouse.getName());
                    log.debug("补充发货仓库名称: {} -> {}", task.getDeparturePoint(), departureWarehouse.getName());
                }
            }

            // 查询收货仓库名称
            if (StringUtils.isNotBlank(task.getArrivalPoint())) {
                Warehouse arrivalWarehouse = warehouseRepository.selectByCode(task.getArrivalPoint());
                if (arrivalWarehouse != null) {
                    task.setArrivalPointName(arrivalWarehouse.getName());
                    log.debug("补充收货仓库名称: {} -> {}", task.getArrivalPoint(), arrivalWarehouse.getName());
                }
            }
        } catch (Exception e) {
            log.warn("补充仓库名称失败: taskId={}, error={}", task.getId(), e.getMessage());
        }
    }

    /**
     * 创建配送任务
     * @param request 配送计划请求
     * @return 创建的配送任务
     */
    @Transactional
    public DeliveryTask createTask(DeliveryPlanRequest request) {
        log.info("开始创建配送任务: droneId={}, planName={}", request.getDroneId(), request.getPlanName());

        CargoType cargoType = cargoTypeRepository.selectByCode(request.getCargoTypeCode());
        if (cargoType == null) {
            throw new ResourceNotFoundException("货物类型找不到: " + request.getCargoTypeCode());
        }

        DeliveryTask task;

        // 如果有订单ID传进来，则根据订单信息创建任务
        if (request.getOrderId() != null) {
            // 根据订单ID查询订单信息
            Order order = orderRepository.findById(request.getOrderId());
            if (order == null) {
                throw new ResourceNotFoundException("订单不存在: " + request.getOrderId());
            }

            // 使用订单信息创建配送任务
            task = request.toDeliveryTask(cargoType);

            // 如果订单存在且有货品重量信息，则更新配送任务的货品重量
            if (order.getCargoWeight() != null) {
                task.setCargoWeight(order.getCargoWeight());
            }

            // 处理无人机ID：优先使用droneId，如果没有则根据aircraftCode查询
            String droneId = task.getDroneId();
            if (droneId == null && request.getAircraftCode() != null) {
                // 根据aircraftCode查询无人机信息
                Drone drone = droneRepository.selectById(request.getAircraftCode());
                if (drone != null) {
                    droneId = drone.getDroneId(); // 使用无人机编码
                    task.setDroneId(droneId);
                    log.info("根据aircraftCode查询到无人机: aircraftCode={}, droneId={}, name={}",
                            request.getAircraftCode(), droneId, drone.getName());
                } else {
                    log.warn("未找到对应的无人机: aircraftCode={}", request.getAircraftCode());
                }
            }
            log.info("设置无人机ID (有订单分支): droneId={}", task.getDroneId());
        } else {
            // 没有订单ID时，直接根据请求参数创建配送任务
            task = new DeliveryTask();
            // 设置基础信息从request
            task.setPlanName(request.getPlanName());
            task.setDeparturePoint(request.getFromWarehouseCode());
            task.setArrivalPoint(request.getRecipientWarehouseCode());
            // 处理无人机ID：优先使用droneId，如果没有则根据aircraftCode查询
            String droneId = request.getDroneId();
            if (droneId == null && request.getAircraftCode() != null) {
                // 根据aircraftCode查询无人机信息
                Drone drone = droneRepository.selectById(request.getAircraftCode());
                if (drone != null) {
                    droneId = drone.getDroneId(); // 使用无人机编码
                    log.info("根据aircraftCode查询到无人机: aircraftCode={}, droneId={}, name={}",
                            request.getAircraftCode(), droneId, drone.getName());
                } else {
                    log.warn("未找到对应的无人机: aircraftCode={}", request.getAircraftCode());
                }
            }
            task.setDroneId(droneId);
            log.info("设置无人机ID (无订单分支): droneId={}", droneId);
            task.setCargoContent(request.getCargoContent());
            task.setCargoTypeCode(cargoType.getCode());
            task.setCargoType(cargoType.getName());
            task.setCargoWeight(request.getCargoWeight());
            task.setReceiverName(request.getReceiverName());
            task.setReceiverPhone(request.getReceiverPhone());
            task.setCreatorName(request.getSenderName());
            task.setCreatorPhone(request.getSenderPhone());
            task.setStatus(DeliveryTaskStatus.PENDING);
            task.setDeliveryPlan(DeliveryPlan.IMMEDIATE);  // 默认立即配送
            task.setCreateTime(LocalDateTime.now());
        }

        // 新增：根据仓库code查询经纬度并计算距离
        Warehouse fromWarehouse = warehouseRepository.selectByCode(request.getFromWarehouseCode());
        Warehouse toWarehouse = warehouseRepository.selectByCode(request.getRecipientWarehouseCode());
        if (fromWarehouse == null || toWarehouse == null) {
            throw new ResourceNotFoundException("发货点或收货点仓库不存在");
        }
        try {
            double fromLat = Double.parseDouble(fromWarehouse.getLatitude());
            double fromLng = Double.parseDouble(fromWarehouse.getLongitude());
            double toLat = Double.parseDouble(toWarehouse.getLatitude());
            double toLng = Double.parseDouble(toWarehouse.getLongitude());
            OsdDroneAlarmDTO fromDto = new OsdDroneAlarmDTO(fromLat, fromLng);
            OsdDroneAlarmDTO toDto = new OsdDroneAlarmDTO(toLat, toLng);
            // 调用DistanceCalculator计算距离（单位：千米），转换为米
            double distanceKm = DistanceCalculator.calculateDistance(fromDto, toDto);
            task.setDeliveryDistance(distanceKm * 1000); // 单位：米

            // 总飞行距离是送货距离的两倍（去程和返程）
            task.setFlightDistance(task.getDeliveryDistance() * 2); // 设置总飞行距离
        } catch (Exception e) {
            log.warn("仓库经纬度格式错误，无法计算距离", e);
        }

        // 无人机编号已在前面设置，这里不需要重复设置

        // 生成取件码（如果还没有的话）
        if (task.getPickupCode() == null || task.getPickupCode().trim().isEmpty()) {
            String pickupCode = String.format("%06d", (int)(Math.random() * 1000000));
            task.setPickupCode(pickupCode);
            log.info("为配送任务生成取件码: taskId={}, pickupCode={}", task.getId(), pickupCode);
        }

        // 保存配送任务
        log.info("保存配送任务到数据库: id={}, droneId={}", task.getId(), task.getDroneId());
        deliveryTaskRepository.insert(task);
        log.info("配送任务保存成功: id={}, droneId={}", task.getId(), task.getDroneId());
        log.info("创建配送任务成功, id={}, 计划名称={}, 距离={}, 总飞行距离={}",
                task.getId(), task.getPlanName(), task.getDeliveryDistance(), task.getFlightDistance());
        return task;
    }

    /**
     * 开始执行配送计划
     * @param planId 配送计划ID
     * @return 更新后的配送任务
     * @throws ResourceNotFoundException 当配送计划不存在时抛出
     * @throws IllegalStateException 当配送计划状态不允许开始时抛出
     * @throws RuntimeException 当飞控系统调用失败时抛出
     */
    @Transactional
    public DeliveryTask startDeliveryPlan(Long planId) {
        // 1. 查找配送任务
        DeliveryTask task = deliveryTaskRepository.selectById(planId);
        if (task == null) {
            throw new ResourceNotFoundException("配送计划不存在: " + planId);
        }

        // 2. 验证任务状态是否允许开始执行
        if (task.getStatus() != DeliveryTaskStatus.PENDING) {
            throw new IllegalStateException("只有待处理状态的配送计划才能开始执行，当前状态: " + task.getStatus().getDescription());
        }

        // 3. 先调用飞控系统创建飞行任务，确保飞控任务创建成功后再更新状态
        CreateFlightTaskResponse flightResponse;
        try {
            log.info("开始为配送任务创建飞行任务: taskId={}, droneId={}", planId, task.getDroneId());

            // 获取当前请求的认证令牌
            String authToken = getCurrentUserAuthToken();
            log.info("使用认证令牌调用飞控接口: planId={}, hasToken={}", planId, authToken != null);

            flightResponse = flightControlService.createFlightTaskForDelivery(task, authToken);

            if (!flightResponse.getSuccess()) {
                String errorMsg = String.format("飞行任务创建失败: %s", flightResponse.getMessage());
                log.warn("飞行任务创建失败: planId={}, error={}", planId, flightResponse.getMessage());
                throw new RuntimeException(errorMsg);
            }

            log.info("飞行任务创建成功: planId={}, flightTaskId={}",
                    planId, flightResponse.getFlightTaskId());

        } catch (Exception e) {
            log.error("调用飞控系统异常: planId={}", planId, e);
            throw new RuntimeException("飞控系统调用失败: " + e.getMessage(), e);
        }

        // 4. 飞控任务创建成功后，更新配送任务状态为取货中
        task.setStatus(DeliveryTaskStatus.PICKING_UP);
        task.setDepartureTime(LocalDateTime.now());

        // 5. 保存更新
        deliveryTaskRepository.updateById(task);

        // 6. 更新关联订单状态为"无人机取货中"
        if (task.getOrderId() != null) {
            LocalDateTime now = LocalDateTime.now();
            int updated = orderRepository.updateStatusAndTime(
                    task.getOrderId(),
                    OrderStatus.DRONE_PICKING_UP.getCode(),
                    now
            );
            if (updated > 0) {
                log.info("更新订单状态为无人机取货中, orderId={}", task.getOrderId());
            }
        }

        // 7. 可以考虑将飞行任务ID保存到配送任务中
        // if (flightResponse.getFlightTaskId() != null) {
        //     task.setFlightTaskId(flightResponse.getFlightTaskId());
        //     deliveryTaskRepository.updateById(task);
        // }

        // 8. 记录操作日志
        log.info("开始执行配送计划: {}, 状态更新为: {}", planId, task.getStatus().getDescription());

        return task;
    }

    /**
     * 获取当前用户的认证令牌
     * @return 认证令牌，如果获取失败则返回null
     */
    private String getCurrentUserAuthToken() {
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes != null) {
                HttpServletRequest request = attributes.getRequest();

                // 首先尝试从 Liangma-Auth header 获取 token
                String token = request.getHeader(SecUtil.LIANGMA_TOKEN);
                if (StringUtils.isNotEmpty(token)) {
                    return token;
                }

                // 如果没有找到，尝试从 Delivery-Auth header 获取 token
                token = request.getHeader(SecUtil.HEADER_TOKEN);
                if (StringUtils.isNotEmpty(token)) {
                    return token;
                }
            }
        } catch (Exception e) {
            log.warn("获取当前用户认证令牌失败", e);
        }
        return null;
    }

    /**
     * 无人机就位
     * @param taskId 任务ID
     * @return 更新后的配送任务
     * @throws ResourceNotFoundException 当任务不存在时抛出
     * @throws IllegalStateException 当任务状态不允许无人机就位时抛出
     */
    @Transactional
    public DeliveryTask droneReady(Long taskId) {
        // 1. 查找配送任务
        DeliveryTask task = deliveryTaskRepository.selectById(taskId);
        if (task == null) {
            throw new ResourceNotFoundException("配送任务不存在: " + taskId);
        }

        // 2. 验证任务状态是否允许无人机就位
        if (task.getStatus() != DeliveryTaskStatus.PICKING_UP) {
            throw new IllegalStateException("只有取货中状态的任务才能进行无人机就位操作，当前状态: " + task.getStatus().getDescription());
        }

        // 3. 更新任务状态为无人机就位
        // 注意：这里可以根据需要添加更多业务逻辑，比如分配无人机等
        task.setStatus(DeliveryTaskStatus.DRONE_READY);
        // 4. 保存更新
        deliveryTaskRepository.updateById(task);

        // 5. 记录操作日志
        log.info("无人机就位, taskId={}", taskId);

        return task;
    }

    /**
     * 标记任务为已取货
     * @param taskId 任务ID
     * @return 更新后的配送任务
     * @throws ResourceNotFoundException 当任务不存在时抛出
     * @throws IllegalStateException 当任务状态不允许标记为已取货时抛出
     */
    @Transactional
    public DeliveryTask markAsPickedUp(Long taskId) {
        // 1. 查找配送任务
        DeliveryTask task = deliveryTaskRepository.selectById(taskId);
        if (task == null) {
            throw new ResourceNotFoundException("配送任务不存在: " + taskId);
        }

        // 2. 验证任务状态是否允许标记为已取货
        if (task.getStatus() != DeliveryTaskStatus.DRONE_READY) {
            throw new IllegalStateException("只有无人机就位状态的任务才能标记为已取货，当前状态: " + task.getStatus().getDescription());
        }

        // 3. 更新任务状态为已取货
        task.setStatus(DeliveryTaskStatus.PICKED_UP);
        task.setReturnTime(LocalDateTime.now());

        // 4. 保存更新
        deliveryTaskRepository.updateById(task);

        // 5. 记录操作日志
        log.info("标记任务已取货, taskId={}", taskId);

        return task;
    }


    /**
     * 标记任务为配送中
     * @param taskId 任务ID
     * @return 更新后的配送任务
     * @throws ResourceNotFoundException 当任务不存在时抛出
     * @throws IllegalStateException 当任务状态不允许标记为配送中时抛出
     */
    @Transactional
    public DeliveryTask markAsDelivering(Long taskId) {
        // 1. 查找配送任务
        DeliveryTask task = deliveryTaskRepository.selectById(taskId);
        if (task == null) {
            throw new ResourceNotFoundException("配送任务不存在: " + taskId);
        }

        // 2. 验证任务状态是否允许标记为配送中
        if (task.getStatus() != DeliveryTaskStatus.PICKED_UP) {
            throw new IllegalStateException("只有已取货状态的任务才能标记为配送中，当前状态: " + task.getStatus().getDescription());
        }

        // 3. 更新任务状态为配送中
        task.setStatus(DeliveryTaskStatus.DELIVERING);
        task.setDeliveryTime(LocalDateTime.now());

        // 4. 保存更新
        deliveryTaskRepository.updateById(task);

        // 5. 更新关联订单状态为"投递至物流仓"
        if (task.getOrderId() != null) {
            LocalDateTime now = LocalDateTime.now();
            int updated = orderRepository.updateStatusAndTime(
                    task.getOrderId(),
                    OrderStatus.DELIVERING_TO_WAREHOUSE.getCode(),
                    now
            );
            if (updated > 0) {
                log.info("更新订单状态为投递至物流仓, orderId={}", task.getOrderId());
            }
        }

        // 6. 记录操作日志
        log.info("标记配送任务为配送中, taskId={}", taskId);

        return task;
    }

    /**
     * 标记任务为已到达
     * @param taskId 任务ID
     * @return 更新后的配送任务
     * @throws ResourceNotFoundException 当任务不存在时抛出
     * @throws IllegalStateException 当任务状态不允许标记为已到达时抛出
     */
    @Transactional
    public DeliveryTask markAsArrived(Long taskId) {
        // 1. 查找配送任务
        DeliveryTask task = deliveryTaskRepository.selectById(taskId);
        if (task == null) {
            throw new ResourceNotFoundException("配送任务不存在: " + taskId);
        }

        // 2. 验证任务状态是否允许标记为已到达
        if (task.getStatus() != DeliveryTaskStatus.DELIVERING) {
            throw new IllegalStateException("只有配送中状态的任务才能标记为已到达，当前状态: " + task.getStatus().getDescription());
        }

        // 3. 更新任务状态为已到达
        task.setStatus(DeliveryTaskStatus.ARRIVED);
        LocalDateTime now = LocalDateTime.now();
        task.setArrivalTime(now);

        // 4. 计算总飞行时长（分钟）
        if (task.getDepartureTime() != null) {
            long flightDuration = java.time.Duration.between(task.getDepartureTime(), now).toMinutes();
            task.setTotalFlightTime((int) flightDuration);
        }

        // 5. 保存更新
        deliveryTaskRepository.updateById(task);

        // 6. 记录操作日志
        log.info("标记任务已到达目的地, taskId={}, 总飞行时长: {}分钟", taskId, task.getTotalFlightTime());

        return task;
    }

    /**
     * 标记任务为已投递
     * @param taskId 任务ID
     * @return 更新后的配送任务
     * @throws ResourceNotFoundException 当任务不存在时抛出
     * @throws IllegalStateException 当任务状态不允许标记为已投递时抛出
     */
    @Transactional
    public DeliveryTask markAsDelivered(Long taskId) {
        // 1. 查找配送任务
        DeliveryTask task = deliveryTaskRepository.selectById(taskId);
        if (task == null) {
            throw new ResourceNotFoundException("配送任务不存在: " + taskId);
        }

        // 2. 验证任务状态是否允许标记为已投递
        if (task.getStatus() != DeliveryTaskStatus.ARRIVED) {
            throw new IllegalStateException("只有已到达状态的任务才能标记为已投递，当前状态: " + task.getStatus().getDescription());
        }

        // 3. 更新任务状态为已投递
        task.setStatus(DeliveryTaskStatus.DELIVERED);
        task.setArrivalTime(LocalDateTime.now());

        // 4. 保存更新
        deliveryTaskRepository.updateById(task);

        // 5. 更新关联订单状态为"配送成功"
        if (task.getOrderId() != null) {
            LocalDateTime now = LocalDateTime.now();
            int updated = orderRepository.updateStatusAndTime(
                    task.getOrderId(),
                    OrderStatus.DELIVERY_SUCCESS.getCode(),
                    now
            );
            if (updated > 0) {
                log.info("更新订单状态为配送成功, orderId={}", task.getOrderId());
            }
        }

        // 6. 发送包裹到达短信通知（包裹已投递，可以取件）
        sendPackageArrivedSms(task);

        // 7. 记录操作日志
        log.info("标记任务已投递, taskId={}", taskId);

        return task;
    }



    /**
     * 分页查询配送任务
     * @param pageNum 页码
     * @param pageSize 每页数量
     * @param keyword 搜索关键词
     * @return 分页结果
     */
    public PageResult<DeliveryTask> listDeliveryTasks(int pageNum, int pageSize, String keyword) {
        // 1. 构建分页参数
        Page<DeliveryTask> page = new Page<>(pageNum, pageSize);

        // 2. 构建查询条件
        LambdaQueryWrapper<DeliveryTask> queryWrapper = new LambdaQueryWrapper<>();

        // 使用工具类构建关键词搜索条件
        QueryUtils.addKeywordSearch(queryWrapper, keyword,
                DeliveryTask::getPlanName,      // 计划名称
                DeliveryTask::getCreatorName,   // 寄件人
                DeliveryTask::getCreatorPhone,  // 寄件人手机号
                DeliveryTask::getReceiverName,  // 收件人
                DeliveryTask::getReceiverPhone, // 收件人手机号
                DeliveryTask::getCargoContent   // 货物内容
        );

        // 3. 使用工具类添加排序条件
        QueryUtils.addOrderByDesc(queryWrapper, DeliveryTask::getCreateTime);

        // 4. 执行分页查询
        com.baomidou.mybatisplus.extension.plugins.pagination.Page<DeliveryTask> taskPage =
                deliveryTaskRepository.selectPage(page, queryWrapper);

        // 5. 构建分页结果
        return PageResult.of(
                taskPage.getCurrent(),
                taskPage.getSize(),
                taskPage.getTotal(),
                taskPage.getRecords()
        );
    }
    public PageResult<DeliveryTask> listDeliveryTasksPage(int pageNum, int pageSize, String senderName, String senderPhone, String recipientName, Integer status) {
        return listDeliveryTasksPage(pageNum, pageSize, senderName, senderPhone, recipientName, status, null, null);
    }

    /**
     * 分页查询配送任务（使用查询DTO）
     * @param query 查询参数DTO
     * @return 分页结果
     */
    public PageResult<DeliveryTask> listDeliveryTasksPage(DeliveryTaskPageQuery query) {
        return listDeliveryTasksPageWithQuery(query);
    }

    /**
     * 分页查询配送任务（支持时间范围查询）
     * @param pageNum 页码
     * @param pageSize 每页数量
     * @param senderName 寄件人姓名
     * @param senderPhone 寄件人手机号
     * @param recipientName 收件人姓名
     * @param status 任务状态
     * @param startDateStr 开始日期字符串 (yyyy-MM-dd)
     * @param endDateStr 结束日期字符串 (yyyy-MM-dd)
     * @return 分页结果
     */
    public PageResult<DeliveryTask> listDeliveryTasksPage(int pageNum, int pageSize, String senderName, String senderPhone, String recipientName, Integer status, String startDateStr, String endDateStr) {
        // 1. 构建分页参数
        Page<DeliveryTask> page = new Page<>(pageNum, pageSize);

        // 2. 构建查询条件
        LambdaQueryWrapper<DeliveryTask> queryWrapper = new LambdaQueryWrapper<>();

        // 2.1 添加模糊查询条件 - 收件人、计划名称、手机号（收件人/寄件人）、发货点、收货点
        if (StringUtils.isNotBlank(senderName)) {
            queryWrapper.like(DeliveryTask::getCreatorName, senderName); // 寄件人
        }
        if (StringUtils.isNotBlank(senderPhone)) {
            queryWrapper.and(wrapper -> wrapper
                    .like(DeliveryTask::getCreatorPhone, senderPhone) // 寄件人手机号
                    .or().like(DeliveryTask::getReceiverPhone, senderPhone)); // 或者收件人手机号
        }
        if (StringUtils.isNotBlank(recipientName)) {
            queryWrapper.like(DeliveryTask::getReceiverName, recipientName); // 收件人
        }

        // 2.2 添加精确查询条件 - 任务状态
        if (status != null) {
            DeliveryTaskStatus taskStatus = convertToDeliveryTaskStatus(status);
            if (taskStatus != null) {
                queryWrapper.eq(DeliveryTask::getStatus, taskStatus);
            }
        }

        // 2.3 添加时间范围查询条件
        if (StringUtils.isNotBlank(startDateStr) || StringUtils.isNotBlank(endDateStr)) {
            try {
                if (StringUtils.isNotBlank(startDateStr)) {
                    LocalDateTime startDateTime = LocalDate.parse(startDateStr).atStartOfDay();
                    queryWrapper.ge(DeliveryTask::getCreateTime, startDateTime);
                    log.info("添加开始时间查询条件: {}", startDateTime);
                }
                if (StringUtils.isNotBlank(endDateStr)) {
                    LocalDateTime endDateTime = LocalDate.parse(endDateStr).atTime(23, 59, 59);
                    queryWrapper.le(DeliveryTask::getCreateTime, endDateTime);
                    log.info("添加结束时间查询条件: {}", endDateTime);
                }
            } catch (Exception e) {
                log.warn("日期格式解析失败: startDate={}, endDate={}, error={}", startDateStr, endDateStr, e.getMessage());
            }
        }

        // 3. 按创建时间倒序
        queryWrapper.orderByDesc(DeliveryTask::getCreateTime);

        // 4. 执行分页查询
        Page<DeliveryTask> taskPage = deliveryTaskRepository.selectPage(page, queryWrapper);

        log.info("查询配送任务完成: 查询条件[senderName={}, senderPhone={}, recipientName={}, status={}, startDate={}, endDate={}], 结果[total={}, current={}]",
                senderName, senderPhone, recipientName, status, startDateStr, endDateStr, taskPage.getTotal(), taskPage.getCurrent());

        return PageResult.of(
                taskPage.getCurrent(),
                taskPage.getSize(),
                taskPage.getTotal(),
                taskPage.getRecords()
        );
    }

    /**
     * 分页查询配送任务（使用查询DTO的具体实现）
     * @param query 查询参数DTO
     * @return 分页结果
     */
    private PageResult<DeliveryTask> listDeliveryTasksPageWithQuery(DeliveryTaskPageQuery query) {
        // 1. 构建分页参数
        Page<DeliveryTask> page = new Page<>(query.getPageNum(), query.getPageSize());

        // 2. 构建查询条件
        LambdaQueryWrapper<DeliveryTask> queryWrapper = new LambdaQueryWrapper<>();

        // 2.1 添加模糊查询条件
        if (StringUtils.isNotBlank(query.getSenderName())) {
            queryWrapper.like(DeliveryTask::getCreatorName, query.getSenderName()); // 寄件人
        }
        if (StringUtils.isNotBlank(query.getSenderPhone())) {
            queryWrapper.and(wrapper -> wrapper
                    .like(DeliveryTask::getCreatorPhone, query.getSenderPhone()) // 寄件人手机号
                    .or().like(DeliveryTask::getReceiverPhone, query.getSenderPhone())); // 或者收件人手机号
        }
        if (StringUtils.isNotBlank(query.getRecipientName())) {
            queryWrapper.like(DeliveryTask::getReceiverName, query.getRecipientName()); // 收件人
        }
        if (StringUtils.isNotBlank(query.getDroneId())) {
            queryWrapper.like(DeliveryTask::getDroneId, query.getDroneId()); // 无人机编号
        }
        if (StringUtils.isNotBlank(query.getPlanName())) {
            queryWrapper.like(DeliveryTask::getPlanName, query.getPlanName()); // 计划名称
        }
        if (StringUtils.isNotBlank(query.getCargoType())) {
            queryWrapper.like(DeliveryTask::getCargoType, query.getCargoType()); // 货物类型
        }
        if (StringUtils.isNotBlank(query.getCargoContent())) {
            queryWrapper.like(DeliveryTask::getCargoContent, query.getCargoContent()); // 货物内容
        }
        if (StringUtils.isNotBlank(query.getDeparturePoint())) {
            queryWrapper.eq(DeliveryTask::getDeparturePoint, query.getDeparturePoint()); // 发货点
        }
        if (StringUtils.isNotBlank(query.getArrivalPoint())) {
            queryWrapper.eq(DeliveryTask::getArrivalPoint, query.getArrivalPoint()); // 收货点
        }

        // 2.2 添加精确查询条件 - 任务状态
        if (query.getStatus() != null) {
            DeliveryTaskStatus taskStatus = convertToDeliveryTaskStatus(query.getStatus());
            if (taskStatus != null) {
                queryWrapper.eq(DeliveryTask::getStatus, taskStatus);
            }
        }

        // 2.3 添加时间范围查询条件
        if (StringUtils.isNotBlank(query.getStartDateStr()) || StringUtils.isNotBlank(query.getEndDateStr())) {
            try {
                if (StringUtils.isNotBlank(query.getStartDateStr())) {
                    LocalDateTime startDateTime = LocalDate.parse(query.getStartDateStr()).atStartOfDay();
                    queryWrapper.ge(DeliveryTask::getCreateTime, startDateTime);
                    log.info("添加开始时间查询条件: {}", startDateTime);
                }
                if (StringUtils.isNotBlank(query.getEndDateStr())) {
                    LocalDateTime endDateTime = LocalDate.parse(query.getEndDateStr()).atTime(23, 59, 59);
                    queryWrapper.le(DeliveryTask::getCreateTime, endDateTime);
                    log.info("添加结束时间查询条件: {}", endDateTime);
                }
            } catch (Exception e) {
                log.warn("日期格式解析失败: startDate={}, endDate={}, error={}",
                        query.getStartDateStr(), query.getEndDateStr(), e.getMessage());
            }
        }

        // 3. 按创建时间倒序
        queryWrapper.orderByDesc(DeliveryTask::getCreateTime);

        // 4. 执行分页查询
        Page<DeliveryTask> taskPage = deliveryTaskRepository.selectPage(page, queryWrapper);

        // 5. 补充仓库名称信息
        for (DeliveryTask task : taskPage.getRecords()) {
            enrichWarehouseNames(task);
        }

        log.info("查询配送任务完成: 查询条件[{}], 结果[total={}, current={}]",
                query, taskPage.getTotal(), taskPage.getCurrent());

        return PageResult.of(
                taskPage.getCurrent(),
                taskPage.getSize(),
                taskPage.getTotal(),
                taskPage.getRecords()
        );
    }

    /**
     * 获取某月每天的配送计划数量
     * @param month 月份(yyyy-MM)
     * @return Map<String, DailyPlanCount>
     */
    public Map<String, DailyPlanCount> getCalendarPlanCountByMonth(String month) {
        // 1. 解析月份字符串并定义日期范围
        YearMonth yearMonth = YearMonth.parse(month, DateTimeFormatter.ofPattern("yyyy-MM"));
        LocalDateTime startOfMonth = yearMonth.atDay(1).atStartOfDay();
        LocalDateTime endOfMonth = yearMonth.atEndOfMonth().atTime(23, 59, 59);

        // 2. 构建查询以获取该月的所有任务
        LambdaQueryWrapper<DeliveryTask> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.between(DeliveryTask::getCreateTime, startOfMonth, endOfMonth);
        List<DeliveryTask> tasks = deliveryTaskRepository.selectList(queryWrapper);

        // 3. 使用Java Streams进行处理
        return tasks.stream()
                .collect(Collectors.groupingBy(
                        task -> task.getCreateTime().toLocalDate().toString(), // 按日期字符串 "yyyy-MM-dd" 分组
                        Collectors.collectingAndThen(
                                Collectors.toList(),
                                dailyTasks -> {
                                    DailyPlanCount counts = new DailyPlanCount();
                                    counts.setTotal(dailyTasks.size());
                                    counts.setPatrolCount((int) dailyTasks.stream().filter(t -> "patrol".equals(t.getPlanName())).count());
                                    counts.setAlarmCount((int) dailyTasks.stream().filter(t -> "alarm".equals(t.getPlanName())).count());
                                    counts.setCollectCount((int) dailyTasks.stream().filter(t -> "collect".equals(t.getPlanName())).count());
                                    counts.setFlyerCount((int) dailyTasks.stream().filter(t -> "flyer".equals(t.getPlanName())).count());
                                    counts.setPoliceCount((int) dailyTasks.stream().filter(t -> "police".equals(t.getPlanName())).count());
                                    counts.setQxsyCount((int) dailyTasks.stream().filter(t -> "qxsy".equals(t.getPlanName())).count());
                                    return counts;
                                }
                        )
                ));
    }


    /**
     * 将数字状态码转换为DeliveryTaskStatus枚举
     * @param statusCode 状态码
     * @return 对应的DeliveryTaskStatus枚举
     */
    private DeliveryTaskStatus convertToDeliveryTaskStatus(Integer statusCode) {
        if (statusCode == null) {
            return null;
        }

        switch (statusCode) {
            case 0: return DeliveryTaskStatus.PENDING;
            case 1: return DeliveryTaskStatus.PICKING_UP;
            case 2: return DeliveryTaskStatus.DRONE_READY;
            case 3: return DeliveryTaskStatus.PICKED_UP;
            case 4: return DeliveryTaskStatus.DELIVERING;
            case 5: return DeliveryTaskStatus.ARRIVED;
            case 6: return DeliveryTaskStatus.DELIVERED;
            case 7: return DeliveryTaskStatus.FAILED;
            case 8: return DeliveryTaskStatus.CANCELLED;
            default: return null;
        }
    }

    /**
     * 获取配送流程信息
     * @param taskId 任务ID
     * @return 配送流程响应
     */
    public DeliveryProcessResponse getDeliveryProcess(Long taskId) {
        // 1. 查询配送任务详情
        DeliveryTask task = getTaskDetailWithOrder(taskId);
        if (task == null) {
            return null;
        }

        // 2. 构建配送流程响应
        return buildDeliveryProcessResponse(task);
    }

    /**
     * 构建配送流程响应
     * @param task 配送任务
     * @return 配送流程响应
     */
    private DeliveryProcessResponse buildDeliveryProcessResponse(DeliveryTask task) {
        // 构建客户信息
        DeliveryProcessResponse.CustomerInfo customerInfo = DeliveryProcessResponse.CustomerInfo.builder()
                .customerName(task.getCreatorName())
                .customerPhone(task.getCreatorPhone())
                .receiverName(task.getReceiverName())
                .receiverPhone(task.getReceiverPhone())
                .build();

        // 构建流程节点
        List<DeliveryProcessResponse.ProcessNode> processNodes = buildProcessNodes(task);

        // 获取当前状态描述
        String currentStatusDesc = task.getStatus() != null ? task.getStatus().getDescription() : "未知";

        return DeliveryProcessResponse.builder()
                .taskId(task.getId())
                .orderNo(task.getOrderNo())
                .currentStatus(task.getStatus() != null ? task.getStatus().name() : null)
                .currentStatusDesc(currentStatusDesc)
                .processNodes(processNodes)
                .customerInfo(customerInfo)
                .build();
    }

    /**
     * 构建流程节点列表
     * @param task 配送任务
     * @return 流程节点列表
     */
    private List<DeliveryProcessResponse.ProcessNode> buildProcessNodes(DeliveryTask task) {
        List<DeliveryProcessResponse.ProcessNode> nodes = new ArrayList<>();
        DeliveryTaskStatus currentStatus = task.getStatus();

        // 定义流程节点顺序和对应的时间字段
        addProcessNode(nodes, DeliveryTaskStatus.PENDING, "预约派件",
                task.getCreateTime(), currentStatus,
                String.format("客户人：%s  手机号：%s",
                        task.getReceiverName() != null ? task.getReceiverName() : "未知",
                        task.getReceiverPhone() != null ? task.getReceiverPhone() : "未知"));

        addProcessNode(nodes, DeliveryTaskStatus.PICKING_UP, "配送至指定点",
                task.getDepartureTime(), currentStatus, null);

        addProcessNode(nodes, DeliveryTaskStatus.DELIVERING, "无人机返回中",
                task.getArrivalTime(), currentStatus, null);

        addProcessNode(nodes, DeliveryTaskStatus.ARRIVED, "投送至指定点",
                task.getDeliveryTime(), currentStatus, null);

        addProcessNode(nodes, DeliveryTaskStatus.DELIVERED, "配送成功未签收",
                task.getReceivedTime(), currentStatus, null);

        addProcessNode(nodes, DeliveryTaskStatus.DELIVERED, "货物已签收",
                task.getReceivedTime(), currentStatus, null);

        addProcessNode(nodes, DeliveryTaskStatus.DELIVERED, "已签收",
                task.getReceivedTime(), currentStatus, null);

        return nodes;
    }

    /**
     * 添加流程节点
     */
    private void addProcessNode(List<DeliveryProcessResponse.ProcessNode> nodes,
                                DeliveryTaskStatus nodeStatus, String statusDesc,
                                LocalDateTime nodeTime, DeliveryTaskStatus currentStatus,
                                String details) {

        boolean completed = isStatusCompleted(nodeStatus, currentStatus);
        boolean current = nodeStatus == currentStatus;

        String nodeTimeStr = null;
        if (nodeTime != null) {
            nodeTimeStr = nodeTime.format(java.time.format.DateTimeFormatter.ofPattern("yyyy.MM.dd HH:mm"));
        }

        DeliveryProcessResponse.ProcessNode node = DeliveryProcessResponse.ProcessNode.builder()
                .status(nodeStatus.name())
                .statusDesc(statusDesc)
                .nodeTime(nodeTime)
                .nodeTimeStr(nodeTimeStr)
                .completed(completed)
                .current(current)
                .details(details)
                .build();

        nodes.add(node);
    }

    /**
     * 判断状态是否已完成
     */
    private boolean isStatusCompleted(DeliveryTaskStatus nodeStatus, DeliveryTaskStatus currentStatus) {
        if (currentStatus == null) {
            return false;
        }

        // 定义状态顺序
        int nodeOrder = getStatusOrder(nodeStatus);
        int currentOrder = getStatusOrder(currentStatus);

        return currentOrder >= nodeOrder;
    }

    /**
     * 获取状态顺序
     */
    private int getStatusOrder(DeliveryTaskStatus status) {
        switch (status) {
            case PENDING: return 1;
            case PICKING_UP: return 2;
            case DELIVERING: return 3;
            case ARRIVED: return 4;
            case DELIVERED: return 5;
            case CANCELLED: return 0;
            case FAILED: return 0;
            default: return 0;
        }
    }

    /**
     * 发送预约寄件短信通知
     *
     * @param task 配送任务
     */
    public void sendOrderPackageSms(DeliveryTask task) {
        try {
            log.info("准备发送预约寄件短信: taskId={}, receiverPhone={}", task.getId(), task.getReceiverPhone());

            // 获取用户ID（这里需要根据手机号查询用户ID，暂时使用模拟数据）
            List<Long> userIds = getUserIdsByPhone(task.getReceiverPhone());

            if (userIds.isEmpty()) {
                log.warn("未找到手机号对应的用户: phone={}", task.getReceiverPhone());
                return;
            }

            // 发送预约寄件短信
            boolean success = smsService.sendOrderPackageSms(
                    task.getTenantId(),
                    userIds,
                    task.getPickupCode()
            );

            if (success) {
                log.info("预约寄件短信发送成功: taskId={}, phone={}", task.getId(), task.getReceiverPhone());
            } else {
                log.error("预约寄件短信发送失败: taskId={}, phone={}", task.getId(), task.getReceiverPhone());
            }

        } catch (Exception e) {
            log.error("发送预约寄件短信异常: taskId={}, error={}", task.getId(), e.getMessage(), e);
        }
    }

    /**
     * 发送包裹到达短信通知
     *
     * @param task 配送任务
     */
    public void sendPackageArrivedSms(DeliveryTask task) {
        try {
            log.info("准备发送包裹到达短信: taskId={}, receiverPhone={}", task.getId(), task.getReceiverPhone());

            // 获取用户ID
            List<Long> userIds = getUserIdsByPhone(task.getReceiverPhone());

            if (userIds.isEmpty()) {
                log.warn("未找到手机号对应的用户: phone={}", task.getReceiverPhone());
                return;
            }

            // 获取目标仓库名称
            String warehouseName = getWarehouseName(task.getArrivalPoint());

            // 发送包裹到达短信
            boolean success = smsService.sendPackageArrivedSms(
                    task.getTenantId(),
                    userIds,
                    warehouseName,
                    task.getPickupCode()
            );

            if (success) {
                log.info("包裹到达短信发送成功: taskId={}, phone={}", task.getId(), task.getReceiverPhone());
            } else {
                log.error("包裹到达短信发送失败: taskId={}, phone={}", task.getId(), task.getReceiverPhone());
            }

        } catch (Exception e) {
            log.error("发送包裹到达短信异常: taskId={}, error={}", task.getId(), e.getMessage(), e);
        }
    }

    /**
     * 根据手机号获取用户ID列表
     *
     * @param phone 手机号
     * @return 用户ID列表
     */
    private List<Long> getUserIdsByPhone(String phone) {
        if (phone == null || phone.trim().isEmpty()) {
            log.warn("手机号为空，无法查询用户");
            return List.of();
        }

        try {
            // 根据手机号查询用户
            User user = userRepository.findByPhone(phone);

            if (user == null) {
                log.warn("未找到手机号对应的用户: phone={}", phone);
                return List.of();
            }

            // 检查用户状态
            if (!user.isEnabled()) {
                log.warn("用户状态不可用: phone={}, status={}", phone, user.getStatus());
                return List.of();
            }

            // 检查外部用户ID是否存在
            if (user.getOuterUserId() == null) {
                log.warn("用户缺少外部用户ID: phone={}, internalId={}, name={}", phone, user.getId(), user.getName());
                return List.of();
            }

            log.debug("找到用户: phone={}, internalId={}, outerUserId={}, name={}",
                    phone, user.getId(), user.getOuterUserId(), user.getName());
            return List.of(user.getOuterUserId()); // 返回外部用户ID，用于短信发送

        } catch (Exception e) {
            log.error("根据手机号查询用户失败: phone={}, error={}", phone, e.getMessage(), e);
            return List.of();
        }
    }

    /**
     * 获取仓库名称
     *
     * @param warehouseCode 仓库编码
     * @return 仓库名称
     */
    private String getWarehouseName(String warehouseCode) {
        if (warehouseCode == null || warehouseCode.trim().isEmpty()) {
            log.warn("仓库编码为空，无法查询仓库名称");
            return "未知物流仓";
        }

        try {
            // 根据编码查询仓库
            Warehouse warehouse = warehouseRepository.selectByCode(warehouseCode);

            if (warehouse == null) {
                log.warn("未找到仓库编码对应的仓库: code={}", warehouseCode);
                // 返回编码作为默认名称
                return warehouseCode + "物流仓";
            }

            log.debug("找到仓库: code={}, name={}, id={}", warehouseCode, warehouse.getName(), warehouse.getId());
            return warehouse.getName();

        } catch (Exception e) {
            log.error("根据编码查询仓库失败: code={}, error={}", warehouseCode, e.getMessage(), e);
            // 发生异常时返回编码作为默认名称
            return warehouseCode + "物流仓";
        }
    }
}