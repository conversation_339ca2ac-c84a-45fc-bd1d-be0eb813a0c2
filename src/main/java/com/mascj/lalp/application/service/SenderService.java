package com.mascj.lalp.application.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mascj.lalp.common.util.PageUtils;
import com.mascj.lalp.common.util.QueryUtils;
import com.mascj.lalp.domain.model.Recipient;
import com.mascj.lalp.domain.model.Sender;
import com.mascj.lalp.domain.repository.SenderRepository;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class SenderService {
    private final SenderRepository senderRepository;
    private final RecipientService recipientService;

    /**
     * 分页查询寄件人列表（支持关键词搜索）
     *
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @param keyword 搜索关键词（姓名或手机号）
     * @return 分页查询结果
     */
    public Page<Sender> pageSenders(int pageNum, int pageSize, String keyword) {
        // 使用工具类创建分页对象
        Page<Sender> page = PageUtils.createPage(pageNum, pageSize);

        // 构建查询条件
        LambdaQueryWrapper<Sender> queryWrapper = new LambdaQueryWrapper<>();

        // 使用工具类添加关键词搜索条件
        QueryUtils.addKeywordSearch(queryWrapper, keyword,
            Sender::getName, Sender::getPhone);

        // 按创建时间倒序排列
        QueryUtils.addOrderByDesc(queryWrapper, Sender::getCreateTime);

        // 执行分页查询
        return senderRepository.selectPage(page, queryWrapper);
    }

    public boolean createSender(Sender sender) {
        return senderRepository.insert(sender) > 0;
    }

    public List<Sender> getSenderList() {
        return senderRepository.selectList(null);
    }

    public List<Sender> searchSenders(String name, String phone) {
        QueryWrapper<Sender> queryWrapper = new QueryWrapper<>();
        if (name != null) {
            queryWrapper.like("name", name);
        }
        if (phone != null) {
            queryWrapper.like("phone", phone);
        }
        return senderRepository.selectList(queryWrapper);
    }

    /**
     * 根据关键词搜索寄件人（姓名或手机号模糊匹配）
     */
    public List<Sender> searchSendersByKeyword(String keyword) {
        if (StringUtils.isBlank(keyword)) {
            return senderRepository.selectList(null);
        }

        LambdaQueryWrapper<Sender> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.and(wrapper ->
            wrapper.like(Sender::getName, keyword)
                  .or()
                  .like(Sender::getPhone, keyword)
        );
        queryWrapper.orderByDesc(Sender::getDeliveryCount)
                   .orderByDesc(Sender::getLastDeliveryTime);

        return senderRepository.selectList(queryWrapper);
    }

    public boolean updateSender(Sender sender) {
        return senderRepository.updateById(sender) > 0;
    }

    /**
     * 获取统一联系人建议（同时查询寄件人和收件人）
     * 业务逻辑：合并两张表的数据，按使用频率排序
     */
    public List<Map<String, Object>> getUnifiedContactSuggestions(String keyword, int limit) {
        // 1. 查询寄件人和收件人（使用OR条件：姓名或手机号包含关键词）
        List<Sender> senders = this.searchSendersByKeyword(keyword);
        List<Recipient> recipients = recipientService.searchRecipientsByKeyword(keyword);

        // 2. 合并数据（使用实体类的转换方法）
        Map<String, Map<String, Object>> contactMap = new HashMap<>();

        // 处理寄件人数据
        for (Sender sender : senders) {
            contactMap.put(sender.getPhone(), sender.toUnifiedContactMap());
        }

        // 处理收件人数据
        for (Recipient recipient : recipients) {
            String phone = recipient.getPhone();
            if (contactMap.containsKey(phone)) {
                // 如果该手机号已存在，则合并信息（使用实体类的合并方法）
                recipient.mergeIntoContactMap(contactMap.get(phone));
            } else {
                // 如果该手机号不存在，则添加收件人信息
                contactMap.put(phone, recipient.toUnifiedContactMap());
            }
        }

        // 3. 排序并限制数量
        return contactMap.values().stream()
                .sorted(this::compareContacts)
                .limit(limit)
                .collect(Collectors.toList());
    }

    /**
     * 联系人比较器：按总使用次数倒序，相同时按最近使用时间倒序
     */
    private int compareContacts(Map<String, Object> a, Map<String, Object> b) {
        // 按总使用次数倒序排列
        int usageCompare = Integer.compare((Integer)b.get("totalUsageCount"), (Integer)a.get("totalUsageCount"));
        if (usageCompare != 0) {
            return usageCompare;
        }

        // 如果使用次数相同，按最近使用时间倒序排列
        LocalDateTime timeA = (LocalDateTime)a.get("lastUsedTime");
        LocalDateTime timeB = (LocalDateTime)b.get("lastUsedTime");
        if (timeA == null && timeB == null) {
            return 0;
        }
        if (timeA == null) {
            return 1;
        }
        if (timeB == null) {
            return -1;
        }
        return timeB.compareTo(timeA);
    }
}