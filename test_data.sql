-- 测试数据：收藏夹和任务收藏关联数据
-- 注意：请根据实际的租户ID和用户ID调整数据

-- 1. 插入收藏夹数据
INSERT INTO `lalp_task_folder` (`id`, `folder_name`, `folder_desc`, `folder_icon`, `folder_color`, `parent_id`, `folder_path`, `folder_level`, `sort_order`, `is_default`, `creator_id`, `creator_name`, `tenant_id`, `create_time`, `update_time`, `deleted`) VALUES
(1, '我的收藏', '默认收藏夹', 'star', '#1890ff', NULL, '/1', 0, 1, 1, 1943228955607216130, '测试用户', 1787645641420480513, '2025-08-05 10:00:00', '2025-08-05 10:00:00', 0),
(2, '紧急任务', '紧急配送任务收藏夹', 'fire', '#ff4d4f', NULL, '/2', 0, 2, 0, 1943228955607216130, '测试用户', 1787645641420480513, '2025-08-05 10:01:00', '2025-08-05 10:01:00', 0),
(3, '重要客户', '重要客户的配送任务', 'crown', '#faad14', NULL, '/3', 0, 3, 0, 1943228955607216130, '测试用户', 1787645641420480513, '2025-08-05 10:02:00', '2025-08-05 10:02:00', 0),
(4, '日常配送', '日常配送任务', 'calendar', '#52c41a', NULL, '/4', 0, 4, 0, 1943228955607216130, '测试用户', 1787645641420480513, '2025-08-05 10:03:00', '2025-08-05 10:03:00', 0);

-- 2. 插入配送任务数据（如果不存在的话）
INSERT INTO `lalp_delivery_task` (`id`, `order_id`, `drone_id`, `departure_point`, `arrival_point`, `departure_time`, `flight_height`, `arrival_time`, `delivery_plan`, `return_time`, `cargo_type`, `cargo_type_code`, `cargo_content`, `cargo_weight`, `receiver_name`, `receiver_phone`, `delivery_time`, `delivery_distance`, `flight_distance`, `status`, `pickup_code`, `delivery_duration`, `total_flight_time`, `creator_name`, `creator_phone`, `plan_name`, `failure_reason`, `create_time`, `tenant_id`, `received_time`) VALUES
(100, 1001, 'DRONE-001', 'WH001', 'WH002', '2025-08-05 09:00:00', 100, '2025-08-05 09:30:00', 'IMMEDIATE', '2025-08-05 10:00:00', '电子产品', 'CT001', '手机配件', 1.50, '张三', '13800138001', '2025-08-05 09:25:00', 15.5, 31.0, 'DELIVERED', 'PC001', 30, 60, '测试用户', '13900139001', '紧急配送', NULL, '2025-08-05 08:30:00', 1787645641420480513, '2025-08-05 09:30:00'),
(101, 1002, 'DRONE-002', 'WH001', 'WH003', '2025-08-05 10:00:00', 120, '2025-08-05 10:45:00', 'IMMEDIATE', '2025-08-05 11:15:00', '文件', 'CT002', '重要文件', 0.80, '李四', '13800138002', '2025-08-05 10:40:00', 22.3, 44.6, 'DELIVERED', 'PC002', 45, 75, '测试用户', '13900139001', '文件配送', NULL, '2025-08-05 09:30:00', 1787645641420480513, '2025-08-05 10:45:00'),
(102, 1003, 'DRONE-001', 'WH002', 'WH004', '2025-08-05 11:00:00', 80, '2025-08-05 11:20:00', 'IMMEDIATE', '2025-08-05 11:50:00', '医疗用品', 'CT003', '药品', 2.20, '王五', '13800138003', '2025-08-05 11:15:00', 8.7, 17.4, 'DELIVERED', 'PC003', 20, 50, '测试用户', '13900139001', '医疗配送', NULL, '2025-08-05 10:30:00', 1787645641420480513, '2025-08-05 11:20:00'),
(103, 1004, 'DRONE-003', 'WH001', 'WH005', '2025-08-05 14:00:00', 150, NULL, 'SCHEDULED', NULL, '食品', 'CT004', '生鲜食品', 3.50, '赵六', '13800138004', NULL, 35.2, 70.4, 'DELIVERING', 'PC004', 0, 0, '测试用户', '13900139001', '定时配送', NULL, '2025-08-05 13:00:00', 1787645641420480513, NULL),
(104, 1005, 'DRONE-002', 'WH003', 'WH006', NULL, 100, NULL, 'IMMEDIATE', NULL, '电子产品', 'CT001', '平板电脑', 1.80, '孙七', '13800138005', NULL, 18.9, 37.8, 'PENDING', 'PC005', 0, 0, '测试用户', '13900139001', '立即配送', NULL, '2025-08-05 14:30:00', 1787645641420480513, NULL);

-- 3. 插入任务收藏关联数据
INSERT INTO `lalp_task_favorite` (`id`, `folder_id`, `task_id`, `favorite_note`, `favorite_tags`, `creator_id`, `creator_name`, `tenant_id`, `create_time`, `update_time`, `deleted`) VALUES
-- 我的收藏夹中的任务
(1, 1, 100, '第一个收藏的任务', '紧急,电子产品', 1943228955607216130, '测试用户', 1787645641420480513, '2025-08-05 10:30:00', '2025-08-05 10:30:00', 0),
(2, 1, 101, '重要文件配送', '文件,重要', 1943228955607216130, '测试用户', 1787645641420480513, '2025-08-05 10:35:00', '2025-08-05 10:35:00', 0),
(3, 1, 102, '医疗紧急配送', '医疗,紧急', 1943228955607216130, '测试用户', 1787645641420480513, '2025-08-05 11:30:00', '2025-08-05 11:30:00', 0),

-- 紧急任务收藏夹中的任务
(4, 2, 100, '紧急电子产品配送', '紧急,高优先级', 1943228955607216130, '测试用户', 1787645641420480513, '2025-08-05 10:32:00', '2025-08-05 10:32:00', 0),
(5, 2, 102, '紧急医疗配送', '医疗,救急', 1943228955607216130, '测试用户', 1787645641420480513, '2025-08-05 11:32:00', '2025-08-05 11:32:00', 0),
(6, 2, 103, '紧急在途任务', '在途,关注', 1943228955607216130, '测试用户', 1787645641420480513, '2025-08-05 14:05:00', '2025-08-05 14:05:00', 0),

-- 重要客户收藏夹中的任务
(7, 3, 101, 'VIP客户文件', 'VIP,重要客户', 1943228955607216130, '测试用户', 1787645641420480513, '2025-08-05 10:37:00', '2025-08-05 10:37:00', 0),
(8, 3, 103, '重要客户配送', '重要客户,食品', 1943228955607216130, '测试用户', 1787645641420480513, '2025-08-05 14:07:00', '2025-08-05 14:07:00', 0),

-- 日常配送收藏夹中的任务
(9, 4, 104, '日常电子产品配送', '日常,电子产品', 1943228955607216130, '测试用户', 1787645641420480513, '2025-08-05 14:35:00', '2025-08-05 14:35:00', 0);

-- 查询验证数据
SELECT '=== 收藏夹列表 ===' as info;
SELECT id, folder_name, creator_id, tenant_id, deleted FROM lalp_task_folder WHERE deleted = 0;

SELECT '=== 配送任务列表 ===' as info;
SELECT id, cargo_content, receiver_name, status, tenant_id FROM lalp_delivery_task WHERE tenant_id = 1787645641420480513;

SELECT '=== 收藏关联列表 ===' as info;
SELECT f.id, f.folder_id, f.task_id, f.favorite_note, f.creator_id, f.tenant_id, f.deleted 
FROM lalp_task_favorite f WHERE f.deleted = 0 AND f.tenant_id = 1787645641420480513;

SELECT '=== 测试查询：所有被收藏的任务 ===' as info;
SELECT t.*, MAX(f.create_time) as latest_favorite_time 
FROM lalp_delivery_task t 
INNER JOIN lalp_task_favorite f ON t.id = f.task_id 
WHERE f.creator_id = 1943228955607216130
AND f.tenant_id = 1787645641420480513
AND f.deleted = 0 
GROUP BY t.id 
ORDER BY latest_favorite_time DESC;
